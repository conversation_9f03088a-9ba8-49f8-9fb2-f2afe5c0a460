using System;
using System.Collections.Generic;

namespace MedicalImageAnalysis.WpfClient.Models;

/// <summary>
/// YOLO检测结果
/// </summary>
public class YoloDetection
{
    /// <summary>
    /// 检测ID
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 标签/类别
    /// </summary>
    public string Label { get; set; } = string.Empty;

    /// <summary>
    /// 置信度
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// 边界框X坐标
    /// </summary>
    public double X { get; set; }

    /// <summary>
    /// 边界框Y坐标
    /// </summary>
    public double Y { get; set; }

    /// <summary>
    /// 边界框宽度
    /// </summary>
    public double Width { get; set; }

    /// <summary>
    /// 边界框高度
    /// </summary>
    public double Height { get; set; }

    /// <summary>
    /// 检测时间
    /// </summary>
    public DateTime DetectionTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 类别ID
    /// </summary>
    public int ClassId { get; set; }

    /// <summary>
    /// 自定义属性
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();

    /// <summary>
    /// 边界框右边界
    /// </summary>
    public double Right => X + Width;

    /// <summary>
    /// 边界框下边界
    /// </summary>
    public double Bottom => Y + Height;

    /// <summary>
    /// 边界框中心X坐标
    /// </summary>
    public double CenterX => X + Width / 2;

    /// <summary>
    /// 边界框中心Y坐标
    /// </summary>
    public double CenterY => Y + Height / 2;

    /// <summary>
    /// 边界框面积
    /// </summary>
    public double Area => Width * Height;
}

/// <summary>
/// YOLO推理配置
/// </summary>
public class YoloInferenceConfig
{
    /// <summary>
    /// 模型路径
    /// </summary>
    public string ModelPath { get; set; } = string.Empty;

    /// <summary>
    /// 置信度阈值
    /// </summary>
    public double Confidence { get; set; } = 0.5;

    /// <summary>
    /// IoU阈值
    /// </summary>
    public double IoU { get; set; } = 0.45;

    /// <summary>
    /// 输入图像大小
    /// </summary>
    public int ImageSize { get; set; } = 640;

    /// <summary>
    /// 最大检测数量
    /// </summary>
    public int MaxDetections { get; set; } = 1000;

    /// <summary>
    /// 是否使用GPU
    /// </summary>
    public bool UseGpu { get; set; } = true;

    /// <summary>
    /// 设备ID（GPU）
    /// </summary>
    public int DeviceId { get; set; } = 0;

    /// <summary>
    /// 批处理大小
    /// </summary>
    public int BatchSize { get; set; } = 1;

    /// <summary>
    /// 是否启用半精度推理
    /// </summary>
    public bool HalfPrecision { get; set; } = false;

    /// <summary>
    /// 类别过滤器（为空表示检测所有类别）
    /// </summary>
    public List<string> ClassFilter { get; set; } = new();

    /// <summary>
    /// 是否保存推理结果
    /// </summary>
    public bool SaveResults { get; set; } = false;

    /// <summary>
    /// 结果保存路径
    /// </summary>
    public string OutputPath { get; set; } = string.Empty;
}

/// <summary>
/// YOLO模型信息
/// </summary>
public class YoloModelInfo
{
    /// <summary>
    /// 模型ID
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 模型名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 模型路径
    /// </summary>
    public string Path { get; set; } = string.Empty;

    /// <summary>
    /// 模型版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 模型描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 支持的类别
    /// </summary>
    public List<string> Classes { get; set; } = new();

    /// <summary>
    /// 输入图像大小
    /// </summary>
    public int InputSize { get; set; } = 640;

    /// <summary>
    /// 模型文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool IsAvailable { get; set; } = true;

    /// <summary>
    /// 模型类型
    /// </summary>
    public string ModelType { get; set; } = "YOLOv11";

    /// <summary>
    /// 格式化的文件大小
    /// </summary>
    public string FormattedFileSize
    {
        get
        {
            if (FileSize < 1024)
                return $"{FileSize} B";
            else if (FileSize < 1024 * 1024)
                return $"{FileSize / 1024.0:F1} KB";
            else if (FileSize < 1024 * 1024 * 1024)
                return $"{FileSize / (1024.0 * 1024.0):F1} MB";
            else
                return $"{FileSize / (1024.0 * 1024.0 * 1024.0):F1} GB";
        }
    }
}
