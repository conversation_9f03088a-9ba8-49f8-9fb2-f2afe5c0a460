using MedicalImageAnalysis.Core.Entities;

namespace MedicalImageAnalysis.Infrastructure.Extensions;

/// <summary>
/// 标注扩展方法
/// </summary>
public static class AnnotationExtensions
{
    /// <summary>
    /// 克隆标注对象
    /// </summary>
    public static Annotation Clone(this Annotation annotation)
    {
        return new Annotation
        {
            Id = annotation.Id,
            Label = annotation.Label,
            BoundingBox = annotation.BoundingBox,
            Confidence = annotation.Confidence,
            CreatedAt = annotation.CreatedAt,
            UpdatedAt = DateTime.UtcNow,
            CreatedBy = annotation.CreatedBy,
            StudyId = annotation.StudyId,
            SeriesId = annotation.SeriesId,
            InstanceId = annotation.InstanceId,
            AnnotationType = annotation.AnnotationType,
            Properties = new Dictionary<string, object>(annotation.Properties),
            IsValidated = annotation.IsValidated,
            ValidationScore = annotation.ValidationScore,
            Comments = annotation.Comments
        };
    }

    /// <summary>
    /// 计算两个标注的重叠面积
    /// </summary>
    public static double CalculateOverlapArea(this Annotation annotation1, Annotation annotation2)
    {
        var rect1 = annotation1.BoundingBox;
        var rect2 = annotation2.BoundingBox;

        var left = Math.Max(rect1.X, rect2.X);
        var top = Math.Max(rect1.Y, rect2.Y);
        var right = Math.Min(rect1.X + rect1.Width, rect2.X + rect2.Width);
        var bottom = Math.Min(rect1.Y + rect1.Height, rect2.Y + rect2.Height);

        if (left < right && top < bottom)
        {
            return (right - left) * (bottom - top);
        }

        return 0;
    }

    /// <summary>
    /// 计算两个标注的IoU（交并比）
    /// </summary>
    public static double CalculateIoU(this Annotation annotation1, Annotation annotation2)
    {
        var overlapArea = annotation1.CalculateOverlapArea(annotation2);
        
        if (overlapArea == 0)
            return 0;

        var area1 = annotation1.BoundingBox.Width * annotation1.BoundingBox.Height;
        var area2 = annotation2.BoundingBox.Width * annotation2.BoundingBox.Height;
        var unionArea = area1 + area2 - overlapArea;

        return unionArea > 0 ? overlapArea / unionArea : 0;
    }

    /// <summary>
    /// 计算标注的中心点
    /// </summary>
    public static (double X, double Y) GetCenter(this Annotation annotation)
    {
        var rect = annotation.BoundingBox;
        return (rect.X + rect.Width / 2.0, rect.Y + rect.Height / 2.0);
    }

    /// <summary>
    /// 计算标注的面积
    /// </summary>
    public static double GetArea(this Annotation annotation)
    {
        return annotation.BoundingBox.Width * annotation.BoundingBox.Height;
    }

    /// <summary>
    /// 计算标注的长宽比
    /// </summary>
    public static double GetAspectRatio(this Annotation annotation)
    {
        var rect = annotation.BoundingBox;
        return rect.Height > 0 ? (double)rect.Width / rect.Height : 0;
    }

    /// <summary>
    /// 检查标注是否在指定区域内
    /// </summary>
    public static bool IsWithinBounds(this Annotation annotation, int imageWidth, int imageHeight)
    {
        var rect = annotation.BoundingBox;
        return rect.X >= 0 && rect.Y >= 0 && 
               rect.X + rect.Width <= imageWidth && 
               rect.Y + rect.Height <= imageHeight;
    }

    /// <summary>
    /// 调整标注边界框以适应图像边界
    /// </summary>
    public static Annotation ClampToBounds(this Annotation annotation, int imageWidth, int imageHeight)
    {
        var cloned = annotation.Clone();
        var rect = cloned.BoundingBox;

        var x = Math.Max(0, Math.Min(rect.X, imageWidth - 1));
        var y = Math.Max(0, Math.Min(rect.Y, imageHeight - 1));
        var width = Math.Max(1, Math.Min(rect.Width, imageWidth - x));
        var height = Math.Max(1, Math.Min(rect.Height, imageHeight - y));

        cloned.BoundingBox = new Rectangle(x, y, width, height);
        return cloned;
    }

    /// <summary>
    /// 扩展标注边界框
    /// </summary>
    public static Annotation ExpandBoundingBox(this Annotation annotation, double factor)
    {
        var cloned = annotation.Clone();
        var rect = cloned.BoundingBox;

        var centerX = rect.X + rect.Width / 2.0;
        var centerY = rect.Y + rect.Height / 2.0;
        var newWidth = (int)(rect.Width * factor);
        var newHeight = (int)(rect.Height * factor);

        cloned.BoundingBox = new Rectangle(
            (int)(centerX - newWidth / 2.0),
            (int)(centerY - newHeight / 2.0),
            newWidth,
            newHeight);

        return cloned;
    }

    /// <summary>
    /// 计算两个标注中心点之间的距离
    /// </summary>
    public static double CalculateDistance(this Annotation annotation1, Annotation annotation2)
    {
        var center1 = annotation1.GetCenter();
        var center2 = annotation2.GetCenter();

        var dx = center1.X - center2.X;
        var dy = center1.Y - center2.Y;

        return Math.Sqrt(dx * dx + dy * dy);
    }

    /// <summary>
    /// 检查标注是否与另一个标注相似
    /// </summary>
    public static bool IsSimilarTo(this Annotation annotation1, Annotation annotation2, double threshold = 0.7)
    {
        // 基于多个特征计算相似度
        var iou = annotation1.CalculateIoU(annotation2);
        var labelMatch = annotation1.Label.Equals(annotation2.Label, StringComparison.OrdinalIgnoreCase) ? 1.0 : 0.0;
        var sizeRatio = Math.Min(annotation1.GetArea(), annotation2.GetArea()) / Math.Max(annotation1.GetArea(), annotation2.GetArea());
        
        var similarity = (iou + labelMatch + sizeRatio) / 3.0;
        return similarity >= threshold;
    }

    /// <summary>
    /// 获取标注的质量分数
    /// </summary>
    public static double GetQualityScore(this Annotation annotation)
    {
        // 基于多个因素计算质量分数
        var confidenceScore = annotation.Confidence;
        var validationScore = annotation.IsValidated ? annotation.ValidationScore : 0.5;
        var sizeScore = Math.Min(1.0, annotation.GetArea() / 10000.0); // 归一化尺寸分数
        
        return (confidenceScore + validationScore + sizeScore) / 3.0;
    }

    /// <summary>
    /// 检查标注是否需要审查
    /// </summary>
    public static bool NeedsReview(this Annotation annotation, double confidenceThreshold = 0.7, double validationThreshold = 0.8)
    {
        return annotation.Confidence < confidenceThreshold || 
               (annotation.IsValidated && annotation.ValidationScore < validationThreshold) ||
               !annotation.IsValidated;
    }

    /// <summary>
    /// 获取标注的描述性统计信息
    /// </summary>
    public static AnnotationStatistics GetStatistics(this IEnumerable<Annotation> annotations)
    {
        var annotationList = annotations.ToList();
        
        if (!annotationList.Any())
        {
            return new AnnotationStatistics();
        }

        var areas = annotationList.Select(a => a.GetArea()).ToList();
        var confidences = annotationList.Select(a => a.Confidence).ToList();
        var aspectRatios = annotationList.Select(a => a.GetAspectRatio()).ToList();

        return new AnnotationStatistics
        {
            Count = annotationList.Count,
            MeanArea = areas.Average(),
            MedianArea = GetMedian(areas),
            MinArea = areas.Min(),
            MaxArea = areas.Max(),
            MeanConfidence = confidences.Average(),
            MedianConfidence = GetMedian(confidences),
            MinConfidence = confidences.Min(),
            MaxConfidence = confidences.Max(),
            MeanAspectRatio = aspectRatios.Average(),
            ValidatedCount = annotationList.Count(a => a.IsValidated),
            LabelDistribution = annotationList.GroupBy(a => a.Label)
                .ToDictionary(g => g.Key, g => g.Count())
        };
    }

    private static double GetMedian(List<double> values)
    {
        var sorted = values.OrderBy(x => x).ToList();
        var count = sorted.Count;
        
        if (count % 2 == 0)
        {
            return (sorted[count / 2 - 1] + sorted[count / 2]) / 2.0;
        }
        else
        {
            return sorted[count / 2];
        }
    }
}

/// <summary>
/// 标注统计信息
/// </summary>
public class AnnotationStatistics
{
    public int Count { get; set; }
    public double MeanArea { get; set; }
    public double MedianArea { get; set; }
    public double MinArea { get; set; }
    public double MaxArea { get; set; }
    public double MeanConfidence { get; set; }
    public double MedianConfidence { get; set; }
    public double MinConfidence { get; set; }
    public double MaxConfidence { get; set; }
    public double MeanAspectRatio { get; set; }
    public int ValidatedCount { get; set; }
    public Dictionary<string, int> LabelDistribution { get; set; } = new();
    
    public double ValidationRate => Count > 0 ? (double)ValidatedCount / Count : 0;
}
