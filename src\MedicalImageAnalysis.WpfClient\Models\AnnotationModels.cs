using System;
using System.Collections.Generic;
using System.Windows;

namespace MedicalImageAnalysis.WpfClient.Models;

/// <summary>
/// 标注信息
/// </summary>
public class AnnotationInfo
{
    /// <summary>
    /// 标注ID
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 标签
    /// </summary>
    public string Label { get; set; } = string.Empty;

    /// <summary>
    /// 置信度
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// 边界框
    /// </summary>
    public Rect BoundingBox { get; set; }

    /// <summary>
    /// 标注类型
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime ModifiedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建者
    /// </summary>
    public string Creator { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 颜色
    /// </summary>
    public string Color { get; set; } = "#FF0000";

    /// <summary>
    /// 是否可见
    /// </summary>
    public bool IsVisible { get; set; } = true;

    /// <summary>
    /// 是否选中
    /// </summary>
    public bool IsSelected { get; set; } = false;

    /// <summary>
    /// 是否锁定
    /// </summary>
    public bool IsLocked { get; set; } = false;

    /// <summary>
    /// 标注点集合（用于多边形标注）
    /// </summary>
    public List<Point> Points { get; set; } = new();

    /// <summary>
    /// 自定义属性
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();

    /// <summary>
    /// 标注面积
    /// </summary>
    public double Area => BoundingBox.Width * BoundingBox.Height;

    /// <summary>
    /// 标注中心点
    /// </summary>
    public Point Center => new(BoundingBox.X + BoundingBox.Width / 2, BoundingBox.Y + BoundingBox.Height / 2);
}

/// <summary>
/// 标注工具类型
/// </summary>
public enum AnnotationToolType
{
    /// <summary>
    /// 选择工具
    /// </summary>
    Select = 0,

    /// <summary>
    /// 矩形
    /// </summary>
    Rectangle = 1,

    /// <summary>
    /// 椭圆
    /// </summary>
    Ellipse = 2,

    /// <summary>
    /// 多边形
    /// </summary>
    Polygon = 3,

    /// <summary>
    /// 自由绘制
    /// </summary>
    Freehand = 4,

    /// <summary>
    /// 直线
    /// </summary>
    Line = 5,

    /// <summary>
    /// 箭头
    /// </summary>
    Arrow = 6,

    /// <summary>
    /// 文本
    /// </summary>
    Text = 7,

    /// <summary>
    /// 测量
    /// </summary>
    Measure = 8
}

/// <summary>
/// 标注配置
/// </summary>
public class AnnotationConfig
{
    /// <summary>
    /// 默认颜色
    /// </summary>
    public string DefaultColor { get; set; } = "#FF0000";

    /// <summary>
    /// 线条粗细
    /// </summary>
    public double LineThickness { get; set; } = 2.0;

    /// <summary>
    /// 是否显示标签
    /// </summary>
    public bool ShowLabels { get; set; } = true;

    /// <summary>
    /// 是否显示置信度
    /// </summary>
    public bool ShowConfidence { get; set; } = true;

    /// <summary>
    /// 字体大小
    /// </summary>
    public double FontSize { get; set; } = 12.0;

    /// <summary>
    /// 字体系列
    /// </summary>
    public string FontFamily { get; set; } = "Microsoft YaHei";

    /// <summary>
    /// 透明度
    /// </summary>
    public double Opacity { get; set; } = 0.8;

    /// <summary>
    /// 是否填充
    /// </summary>
    public bool IsFilled { get; set; } = false;

    /// <summary>
    /// 填充透明度
    /// </summary>
    public double FillOpacity { get; set; } = 0.3;

    /// <summary>
    /// 最小置信度阈值
    /// </summary>
    public double MinConfidence { get; set; } = 0.5;

    /// <summary>
    /// 是否启用自动保存
    /// </summary>
    public bool AutoSave { get; set; } = true;

    /// <summary>
    /// 自动保存间隔（秒）
    /// </summary>
    public int AutoSaveInterval { get; set; } = 30;
}
