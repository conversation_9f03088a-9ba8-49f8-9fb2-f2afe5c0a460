using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Models;
using System.Numerics;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// 高级图像处理服务接口
/// </summary>
public interface IAdvancedImageProcessingService
{
    /// <summary>
    /// 多平面重建 (MPR)
    /// </summary>
    Task<MultiPlanarReconstructionResult> MultiPlanarReconstructionAsync(
        List<PixelData> volumeData, 
        ReconstructionPlane plane, 
        int sliceIndex,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 高级边缘检测
    /// </summary>
    Task<PixelData> AdvancedEdgeDetectionAsync(
        PixelData pixelData, 
        EdgeDetectionMethod method, 
        double threshold = 0.1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 形态学操作
    /// </summary>
    Task<PixelData> MorphologicalOperationAsync(
        PixelData pixelData, 
        MorphologicalOperation operation, 
        StructuringElement structuringElement,
        int iterations = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 频域滤波
    /// </summary>
    Task<PixelData> FrequencyDomainFilterAsync(
        PixelData pixelData, 
        FrequencyFilter filter, 
        double cutoffFrequency = 0.5,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 纹理分析
    /// </summary>
    Task<TextureAnalysisResult> TextureAnalysisAsync(
        PixelData pixelData, 
        TextureFeatures features,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 图像配准
    /// </summary>
    Task<ImageRegistrationResult> ImageRegistrationAsync(
        PixelData fixedImage, 
        PixelData movingImage, 
        RegistrationMethod method,
        RegistrationParameters parameters,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 重建平面枚举
/// </summary>
public enum ReconstructionPlane
{
    /// <summary>
    /// 轴位面
    /// </summary>
    Axial = 1,
    /// <summary>
    /// 矢状面
    /// </summary>
    Sagittal = 2,
    /// <summary>
    /// 冠状面
    /// </summary>
    Coronal = 3,
    /// <summary>
    /// 斜面
    /// </summary>
    Oblique = 4
}

/// <summary>
/// 边缘检测方法枚举
/// </summary>
public enum EdgeDetectionMethod
{
    /// <summary>
    /// Sobel算子
    /// </summary>
    Sobel = 1,
    /// <summary>
    /// Canny边缘检测
    /// </summary>
    Canny = 2,
    /// <summary>
    /// Laplacian算子
    /// </summary>
    Laplacian = 3,
    /// <summary>
    /// Roberts算子
    /// </summary>
    Roberts = 4,
    /// <summary>
    /// Prewitt算子
    /// </summary>
    Prewitt = 5,
    /// <summary>
    /// Scharr算子
    /// </summary>
    Scharr = 6
}

/// <summary>
/// 形态学操作枚举
/// </summary>
public enum MorphologicalOperation
{
    /// <summary>
    /// 腐蚀操作
    /// </summary>
    Erosion = 1,
    /// <summary>
    /// 膨胀操作
    /// </summary>
    Dilation = 2,
    /// <summary>
    /// 开运算
    /// </summary>
    Opening = 3,
    /// <summary>
    /// 闭运算
    /// </summary>
    Closing = 4,
    /// <summary>
    /// 形态学梯度
    /// </summary>
    Gradient = 5,
    /// <summary>
    /// 顶帽变换
    /// </summary>
    TopHat = 6,
    /// <summary>
    /// 黑帽变换
    /// </summary>
    BlackHat = 7
}

/// <summary>
/// 频域滤波器枚举
/// </summary>
public enum FrequencyFilter
{
    /// <summary>
    /// 低通滤波
    /// </summary>
    LowPass = 1,
    /// <summary>
    /// 高通滤波
    /// </summary>
    HighPass = 2,
    /// <summary>
    /// 带通滤波
    /// </summary>
    BandPass = 3,
    /// <summary>
    /// 带阻滤波
    /// </summary>
    BandStop = 4,
    /// <summary>
    /// 陷波滤波
    /// </summary>
    Notch = 5
}

/// <summary>
/// 纹理特征枚举
/// </summary>
[Flags]
public enum TextureFeatures
{
    /// <summary>
    /// 无纹理特征
    /// </summary>
    None = 0,
    /// <summary>
    /// 灰度共生矩阵特征
    /// </summary>
    GLCM = 1,
    /// <summary>
    /// 局部二值模式特征
    /// </summary>
    LBP = 2,
    /// <summary>
    /// Gabor滤波器特征
    /// </summary>
    Gabor = 4,
    /// <summary>
    /// 小波变换特征
    /// </summary>
    Wavelet = 8,
    /// <summary>
    /// 所有纹理特征
    /// </summary>
    All = GLCM | LBP | Gabor | Wavelet
}

/// <summary>
/// 配准方法枚举
/// </summary>
public enum RegistrationMethod
{
    /// <summary>
    /// 刚性配准
    /// </summary>
    Rigid = 1,
    /// <summary>
    /// 仿射配准
    /// </summary>
    Affine = 2,
    /// <summary>
    /// 可变形配准
    /// </summary>
    Deformable = 3,
    /// <summary>
    /// Demons算法
    /// </summary>
    Demons = 4,
    /// <summary>
    /// B样条配准
    /// </summary>
    BSpline = 5
}

/// <summary>
/// 结构元素
/// </summary>
public class StructuringElement
{
    /// <summary>
    /// 结构元素核
    /// </summary>
    public int[,] Kernel { get; set; } = new int[3, 3] { { 1, 1, 1 }, { 1, 1, 1 }, { 1, 1, 1 } };

    /// <summary>
    /// 结构元素宽度
    /// </summary>
    public int Width => Kernel.GetLength(1);

    /// <summary>
    /// 结构元素高度
    /// </summary>
    public int Height => Kernel.GetLength(0);

    /// <summary>
    /// 创建矩形结构元素
    /// </summary>
    /// <param name="width">宽度</param>
    /// <param name="height">高度</param>
    /// <returns>矩形结构元素</returns>
    public static StructuringElement Rectangle(int width, int height)
    {
        var kernel = new int[height, width];
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                kernel[y, x] = 1;
            }
        }
        return new StructuringElement { Kernel = kernel };
    }
    
    /// <summary>
    /// 创建圆形结构元素
    /// </summary>
    /// <param name="radius">半径</param>
    /// <returns>圆形结构元素</returns>
    public static StructuringElement Circle(int radius)
    {
        var size = radius * 2 + 1;
        var kernel = new int[size, size];
        var center = radius;
        
        for (int y = 0; y < size; y++)
        {
            for (int x = 0; x < size; x++)
            {
                var distance = Math.Sqrt((x - center) * (x - center) + (y - center) * (y - center));
                kernel[y, x] = distance <= radius ? 1 : 0;
            }
        }
        
        return new StructuringElement { Kernel = kernel };
    }
}

/// <summary>
/// 配准参数
/// </summary>
public class RegistrationParameters
{
    /// <summary>
    /// 最大迭代次数
    /// </summary>
    public int MaxIterations { get; set; } = 100;

    /// <summary>
    /// 收敛容差
    /// </summary>
    public double Tolerance { get; set; } = 1e-6;

    /// <summary>
    /// 相似性度量方法
    /// </summary>
    public string SimilarityMetric { get; set; } = "NormalizedCrossCorrelation";

    /// <summary>
    /// 优化器类型
    /// </summary>
    public string Optimizer { get; set; } = "GradientDescent";

    /// <summary>
    /// 学习率
    /// </summary>
    public double LearningRate { get; set; } = 0.01;

    /// <summary>
    /// 附加参数
    /// </summary>
    public Dictionary<string, object> AdditionalParameters { get; set; } = new();
}

/// <summary>
/// 多平面重建结果
/// </summary>
public class MultiPlanarReconstructionResult
{
    /// <summary>
    /// 重建是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 重建平面
    /// </summary>
    public ReconstructionPlane Plane { get; set; }

    /// <summary>
    /// 切片索引
    /// </summary>
    public int SliceIndex { get; set; }

    /// <summary>
    /// 重建后的图像
    /// </summary>
    public PixelData? ReconstructedImage { get; set; }

    /// <summary>
    /// 处理时间（毫秒）
    /// </summary>
    public double ProcessingTimeMs { get; set; }
}

/// <summary>
/// 纹理分析结果
/// </summary>
public class TextureAnalysisResult
{
    /// <summary>
    /// 灰度共生矩阵特征
    /// </summary>
    public GLCMFeatures? GLCMFeatures { get; set; }

    /// <summary>
    /// 局部二值模式特征
    /// </summary>
    public LBPFeatures? LBPFeatures { get; set; }

    /// <summary>
    /// Gabor滤波器特征
    /// </summary>
    public GaborFeatures? GaborFeatures { get; set; }

    /// <summary>
    /// 小波变换特征
    /// </summary>
    public WaveletFeatures? WaveletFeatures { get; set; }
}

/// <summary>
/// GLCM特征
/// </summary>
public class GLCMFeatures
{
    /// <summary>
    /// 对比度
    /// </summary>
    public double Contrast { get; set; }

    /// <summary>
    /// 相关性
    /// </summary>
    public double Correlation { get; set; }

    /// <summary>
    /// 能量
    /// </summary>
    public double Energy { get; set; }

    /// <summary>
    /// 同质性
    /// </summary>
    public double Homogeneity { get; set; }

    /// <summary>
    /// 熵
    /// </summary>
    public double Entropy { get; set; }
}

/// <summary>
/// LBP特征
/// </summary>
public class LBPFeatures
{
    public double[] Histogram { get; set; } = Array.Empty<double>();
    public double Uniformity { get; set; }
    public double Variance { get; set; }
}

/// <summary>
/// Gabor特征
/// </summary>
public class GaborFeatures
{
    public double[] Responses { get; set; } = Array.Empty<double>();
    public double Mean { get; set; }
    public double StandardDeviation { get; set; }
}

/// <summary>
/// 小波特征
/// </summary>
public class WaveletFeatures
{
    public double[] Coefficients { get; set; } = Array.Empty<double>();
    public double Energy { get; set; }
    public double Entropy { get; set; }
}

/// <summary>
/// 图像配准结果
/// </summary>
public class ImageRegistrationResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public Matrix4x4 TransformMatrix { get; set; }
    public PixelData? RegisteredImage { get; set; }
    public double SimilarityMetric { get; set; }
    public int Iterations { get; set; }
    public double ProcessingTimeMs { get; set; }
}
