using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Infrastructure.Services;
using Microsoft.Extensions.Logging;
using System.Windows;
using System.Windows.Controls;

namespace MedicalImageAnalysis.Wpf.Views;

/// <summary>
/// 增强的图像处理视图
/// </summary>
public partial class EnhancedImageProcessingView : UserControl
{
    private readonly ILogger<EnhancedImageProcessingView> _logger;
    private readonly IAdvancedImageProcessingService _advancedImageProcessingService;
    private readonly ISmartAnnotationService _smartAnnotationService;
    private readonly TrainingMonitoringService _trainingMonitoringService;
    private readonly ModelEvaluationService _modelEvaluationService;

    private PixelData? _currentPixelData;
    private DicomInstance? _currentDicomInstance;

    public EnhancedImageProcessingView(
        ILogger<EnhancedImageProcessingView> logger,
        IAdvancedImageProcessingService advancedImageProcessingService,
        ISmartAnnotationService smartAnnotationService,
        TrainingMonitoringService trainingMonitoringService,
        ModelEvaluationService modelEvaluationService)
    {
        InitializeComponent();
        
        _logger = logger;
        _advancedImageProcessingService = advancedImageProcessingService;
        _smartAnnotationService = smartAnnotationService;
        _trainingMonitoringService = trainingMonitoringService;
        _modelEvaluationService = modelEvaluationService;

        InitializeUI();
    }

    private void InitializeUI()
    {
        // 初始化边缘检测方法下拉框
        EdgeDetectionMethodComboBox.ItemsSource = Enum.GetValues<EdgeDetectionMethod>();
        EdgeDetectionMethodComboBox.SelectedItem = EdgeDetectionMethod.Canny;

        // 初始化形态学操作下拉框
        MorphologyOperationComboBox.ItemsSource = Enum.GetValues<MorphologyOperation>();
        MorphologyOperationComboBox.SelectedItem = MorphologyOperation.Opening;

        // 初始化图像增强类型下拉框
        EnhancementTypeComboBox.ItemsSource = Enum.GetValues<ImageEnhancementType>();
        EnhancementTypeComboBox.SelectedItem = ImageEnhancementType.ContrastAdjustment;
    }

    /// <summary>
    /// 应用边缘检测
    /// </summary>
    private async void ApplyEdgeDetection_Click(object sender, RoutedEventArgs e)
    {
        if (_currentPixelData == null)
        {
            MessageBox.Show("请先加载图像", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        try
        {
            ShowProgress("正在应用边缘检测...");

            var method = (EdgeDetectionMethod)EdgeDetectionMethodComboBox.SelectedItem;
            var threshold = EdgeDetectionThresholdSlider.Value;

            var result = await _advancedImageProcessingService.ApplyEdgeDetectionAsync(
                _currentPixelData, method, threshold);

            // 显示结果
            DisplayProcessedImage(result);
            UpdateStatus($"边缘检测完成 - 方法: {method}, 阈值: {threshold:F2}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "边缘检测失败");
            MessageBox.Show($"边缘检测失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            HideProgress();
        }
    }

    /// <summary>
    /// 应用形态学操作
    /// </summary>
    private async void ApplyMorphology_Click(object sender, RoutedEventArgs e)
    {
        if (_currentPixelData == null)
        {
            MessageBox.Show("请先加载图像", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        try
        {
            ShowProgress("正在应用形态学操作...");

            var operation = (MorphologyOperation)MorphologyOperationComboBox.SelectedItem;
            var kernelSize = (int)MorphologyKernelSizeSlider.Value;

            var result = await _advancedImageProcessingService.ApplyMorphologyAsync(
                _currentPixelData, operation, kernelSize);

            DisplayProcessedImage(result);
            UpdateStatus($"形态学操作完成 - 操作: {operation}, 核大小: {kernelSize}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "形态学操作失败");
            MessageBox.Show($"形态学操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            HideProgress();
        }
    }

    /// <summary>
    /// 生成智能标注推荐
    /// </summary>
    private async void GenerateSmartAnnotations_Click(object sender, RoutedEventArgs e)
    {
        if (_currentDicomInstance == null)
        {
            MessageBox.Show("请先加载DICOM图像", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        try
        {
            ShowProgress("正在生成智能标注推荐...");

            var config = new SmartAnnotationConfig
            {
                UseMultiModelEnsemble = true,
                EnableContextAnalysis = true,
                EnableAnatomyDetection = true,
                ConfidenceThreshold = 0.5
            };

            var result = await _smartAnnotationService.GenerateSmartAnnotationsAsync(
                _currentDicomInstance, config);

            if (result.Success && result.FinalAnnotations.Any())
            {
                DisplayAnnotationRecommendations(result.FinalAnnotations);
                UpdateStatus($"生成了 {result.FinalAnnotations.Count} 个智能标注推荐");
            }
            else
            {
                UpdateStatus("未生成智能标注推荐");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "智能标注生成失败");
            MessageBox.Show($"智能标注生成失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            HideProgress();
        }
    }

    /// <summary>
    /// 开始模型训练
    /// </summary>
    private async void StartModelTraining_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var config = new TrainingSessionConfig
            {
                ModelType = "YOLOv11",
                DatasetPath = DatasetPathTextBox.Text,
                OutputPath = OutputPathTextBox.Text,
                Epochs = (int)EpochsSlider.Value,
                BatchSize = (int)BatchSizeSlider.Value,
                LearningRate = LearningRateSlider.Value,
                EnableEarlyStopping = EarlyStoppingCheckBox.IsChecked ?? false
            };

            if (string.IsNullOrEmpty(config.DatasetPath) || string.IsNullOrEmpty(config.OutputPath))
            {
                MessageBox.Show("请设置数据集路径和输出路径", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            ShowProgress("正在启动模型训练...");

            var sessionId = await _trainingMonitoringService.StartTrainingSessionAsync(config);
            
            // 启动训练监控
            StartTrainingMonitoring(sessionId);

            UpdateStatus($"模型训练已启动 - 会话ID: {sessionId}");
            MessageBox.Show($"模型训练已启动\n会话ID: {sessionId}", "训练启动", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动模型训练失败");
            MessageBox.Show($"启动模型训练失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            HideProgress();
        }
    }

    /// <summary>
    /// 评估模型
    /// </summary>
    private async void EvaluateModel_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var config = new ModelEvaluationConfig
            {
                ModelPath = ModelPathTextBox.Text,
                TestDataPath = TestDataPathTextBox.Text,
                OutputPath = EvaluationOutputPathTextBox.Text,
                EvaluatePerformance = true,
                EvaluateSpeed = true,
                EvaluateRobustness = RobustnessCheckBox.IsChecked ?? false
            };

            if (string.IsNullOrEmpty(config.ModelPath) || string.IsNullOrEmpty(config.TestDataPath))
            {
                MessageBox.Show("请设置模型路径和测试数据路径", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            ShowProgress("正在评估模型...");

            var progress = new Progress<ModelEvaluationProgress>(p =>
            {
                Dispatcher.Invoke(() =>
                {
                    EvaluationProgressBar.Value = p.Progress;
                    EvaluationStatusText.Text = p.Message;
                });
            });

            var result = await _modelEvaluationService.EvaluateModelAsync(config, progress);

            if (result.Success)
            {
                DisplayEvaluationResults(result);
                UpdateStatus("模型评估完成");
                MessageBox.Show("模型评估完成！", "评估完成", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show($"模型评估失败: {result.ErrorMessage}", "评估失败", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "模型评估失败");
            MessageBox.Show($"模型评估失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            HideProgress();
        }
    }

    private void DisplayProcessedImage(PixelData processedData)
    {
        // 实现图像显示逻辑
        _currentPixelData = processedData;
        // 这里应该将PixelData转换为WPF可显示的格式
    }

    private void DisplayAnnotationRecommendations(List<Annotation> annotations)
    {
        // 实现标注推荐显示逻辑
        AnnotationRecommendationsListBox.ItemsSource = annotations.Select(a => new
        {
            Label = a.Label,
            Confidence = $"{a.Confidence:P1}",
            BoundingBox = $"({a.BoundingBox.CenterX:F2}, {a.BoundingBox.CenterY:F2})"
        });
    }

    private void DisplayEvaluationResults(ModelEvaluationResult result)
    {
        // 实现评估结果显示逻辑
        if (result.PerformanceMetrics != null)
        {
            Map50Text.Text = $"{result.PerformanceMetrics.Map50:P2}";
            Map5095Text.Text = $"{result.PerformanceMetrics.Map5095:P2}";
            PrecisionText.Text = $"{result.PerformanceMetrics.Precision:P2}";
            RecallText.Text = $"{result.PerformanceMetrics.Recall:P2}";
        }

        if (result.SpeedMetrics != null)
        {
            AvgInferenceTimeText.Text = $"{result.SpeedMetrics.AverageInferenceTimeMs:F2} ms";
            FpsText.Text = $"{result.SpeedMetrics.ImagesPerSecond:F1} FPS";
        }
    }

    private void StartTrainingMonitoring(string sessionId)
    {
        // 启动训练监控定时器
        var timer = new System.Windows.Threading.DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(2)
        };

        timer.Tick += (s, e) =>
        {
            var session = _trainingMonitoringService.GetTrainingSession(sessionId);
            if (session != null)
            {
                TrainingProgressBar.Value = session.Progress;
                TrainingStatusText.Text = $"Epoch {session.CurrentEpoch}/{session.TotalEpochs}";
                CurrentLossText.Text = $"{session.Metrics.CurrentTrainLoss:F4}";
                CurrentAccuracyText.Text = $"{session.Metrics.CurrentTrainAccuracy:P2}";

                if (session.Status == TrainingStatus.Completed || 
                    session.Status == TrainingStatus.Failed || 
                    session.Status == TrainingStatus.Stopped)
                {
                    timer.Stop();
                    MessageBox.Show($"训练完成，状态: {session.Status}", "训练完成", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        };

        timer.Start();
    }

    private void ShowProgress(string message)
    {
        ProgressBar.Visibility = Visibility.Visible;
        ProgressText.Text = message;
        ProgressBar.IsIndeterminate = true;
    }

    private void HideProgress()
    {
        ProgressBar.Visibility = Visibility.Collapsed;
        ProgressBar.IsIndeterminate = false;
    }

    private void UpdateStatus(string message)
    {
        StatusText.Text = message;
        _logger.LogInformation(message);
    }
}
