using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using System.IO;
using System.Windows;
using MedicalImageAnalysis.WpfClient.Services;
using MedicalImageAnalysis.WpfClient.ViewModels;
using MedicalImageAnalysis.WpfClient.Views;

namespace MedicalImageAnalysis.WpfClient;

/// <summary>
/// WPF应用程序主类
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    /// <summary>
    /// 服务提供者
    /// </summary>
    public IServiceProvider? ServiceProvider => _host?.Services;

    protected override async void OnStartup(StartupEventArgs e)
    {
        // 设置全局异常处理
        SetupGlobalExceptionHandling();

        // 配置Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .WriteTo.Console()
            .WriteTo.File("logs/wpf-client-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        Log.Information("应用程序启动");

        try
        {
            // 创建主机
            _host = CreateHostBuilder(e.Args).Build();

            // 启动主机
            await _host.StartAsync();

            // 显示主窗口
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "应用程序启动失败");
            MessageBox.Show($"应用程序启动失败: {ex.Message}\n\n应用程序将尝试以受限模式运行。",
                          "启动错误", MessageBoxButton.OK, MessageBoxImage.Warning);

            // 不强制关闭，让应用程序尝试继续运行
            Log.Warning("应用程序将尝试以受限模式继续运行");
        }
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        try
        {
            Log.Information("应用程序正在退出");

            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }
        }
        catch (Exception ex)
        {
            Log.Error(ex, "应用程序退出时发生错误");
        }
        finally
        {
            Log.CloseAndFlush();
            base.OnExit(e);
        }
    }

    /// <summary>
    /// 设置全局异常处理
    /// </summary>
    private void SetupGlobalExceptionHandling()
    {
        // 处理UI线程未捕获的异常
        DispatcherUnhandledException += (sender, e) =>
        {
            Log.Error(e.Exception, "UI线程发生未捕获的异常");

            var result = MessageBox.Show(
                $"应用程序发生错误:\n{e.Exception.Message}\n\n是否继续运行？",
                "应用程序错误",
                MessageBoxButton.YesNo,
                MessageBoxImage.Error);

            if (result == MessageBoxResult.Yes)
            {
                e.Handled = true; // 标记异常已处理，继续运行
            }
            else
            {
                e.Handled = false; // 让应用程序崩溃
            }
        };

        // 处理非UI线程未捕获的异常
        AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
        {
            var exception = e.ExceptionObject as Exception;
            Log.Fatal(exception, "应用程序域发生未捕获的异常，IsTerminating: {IsTerminating}", e.IsTerminating);

            if (e.IsTerminating)
            {
                MessageBox.Show(
                    $"应用程序发生严重错误，即将退出:\n{exception?.Message}",
                    "严重错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        };

        // 处理Task中未观察到的异常
        TaskScheduler.UnobservedTaskException += (sender, e) =>
        {
            Log.Error(e.Exception, "Task中发生未观察到的异常");
            e.SetObserved(); // 标记异常已观察，防止应用程序崩溃
        };
    }

    private static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureAppConfiguration((context, config) =>
            {
                // 设置基础路径为应用程序目录
                var basePath = AppDomain.CurrentDomain.BaseDirectory;
                config.SetBasePath(basePath);

                // 添加配置文件
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true);
                config.AddEnvironmentVariables();
                config.AddCommandLine(args);
            })
            .ConfigureServices((context, services) =>
            {
                // 配置服务
                ConfigureServices(services, context.Configuration);
            });

    private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // HTTP客户端
        services.AddHttpClient<ApiService>(client =>
        {
            client.Timeout = TimeSpan.FromMinutes(5);
        });

        // 应用服务
        services.AddSingleton<IApiService, ApiService>();
        services.AddSingleton<ISignalRService, SignalRService>();
        services.AddSingleton<IFileService, FileService>();
        services.AddSingleton<IDialogService, DialogService>();
        services.AddSingleton<ISettingsService, SettingsService>();
        services.AddSingleton<INotificationService, NotificationService>();
        services.AddSingleton<IImageProcessingService, ImageProcessingService>();
        services.AddSingleton<IYoloInferenceService, YoloInferenceService>();
        services.AddSingleton<DicomTestService>();

        // ViewModels
        services.AddTransient<MainWindowViewModel>();
        services.AddTransient<DicomViewerViewModel>();
        services.AddTransient<AnnotationViewModel>();
        services.AddTransient<ModelTrainingViewModel>();
        services.AddTransient<DataManagementViewModel>();
        services.AddTransient<SystemMonitorViewModel>();

        // Views
        services.AddTransient<MainWindow>();

        // 其他服务
        services.AddSingleton<IConfiguration>(configuration);
    }
}
