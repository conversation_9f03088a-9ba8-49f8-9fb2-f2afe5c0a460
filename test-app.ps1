# 测试WPF应用程序启动脚本
Write-Host "开始测试医学影像解析系统..." -ForegroundColor Green

# 构建项目
Write-Host "正在构建项目..." -ForegroundColor Yellow
$buildResult = dotnet build src/MedicalImageAnalysis.WpfClient --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 构建失败" -ForegroundColor Red
    exit 1
}
Write-Host "✅ 构建成功" -ForegroundColor Green

# 启动应用程序（后台运行）
Write-Host "正在启动应用程序..." -ForegroundColor Yellow
$process = Start-Process -FilePath "dotnet" -ArgumentList "run --project src/MedicalImageAnalysis.WpfClient" -PassThru -WindowStyle Hidden

# 等待5秒
Start-Sleep -Seconds 5

# 检查进程是否还在运行
if ($process.HasExited) {
    Write-Host "❌ 应用程序启动失败或立即退出" -ForegroundColor Red
    Write-Host "退出代码: $($process.ExitCode)" -ForegroundColor Red
} else {
    Write-Host "✅ 应用程序启动成功，正在运行中" -ForegroundColor Green
    Write-Host "进程ID: $($process.Id)" -ForegroundColor Cyan
    
    # 终止进程
    Write-Host "正在终止应用程序..." -ForegroundColor Yellow
    $process.Kill()
    $process.WaitForExit()
    Write-Host "✅ 应用程序已正常终止" -ForegroundColor Green
}

Write-Host "测试完成！" -ForegroundColor Green
