using System;
using System.Collections.Generic;

namespace MedicalImageAnalysis.WpfClient.Models;

/// <summary>
/// DICOM信息
/// </summary>
public class DicomInfo
{
    /// <summary>
    /// DICOM实例ID
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string PatientName { get; set; } = string.Empty;

    /// <summary>
    /// 患者ID
    /// </summary>
    public string PatientId { get; set; } = string.Empty;

    /// <summary>
    /// 研究描述
    /// </summary>
    public string StudyDescription { get; set; } = string.Empty;

    /// <summary>
    /// 序列描述
    /// </summary>
    public string SeriesDescription { get; set; } = string.Empty;

    /// <summary>
    /// 模态
    /// </summary>
    public string Modality { get; set; } = string.Empty;

    /// <summary>
    /// 研究日期
    /// </summary>
    public DateTime StudyDate { get; set; }

    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 图像尺寸
    /// </summary>
    public (int Width, int Height) ImageSize { get; set; }

    /// <summary>
    /// 像素间距
    /// </summary>
    public (double X, double Y) PixelSpacing { get; set; }

    /// <summary>
    /// 层厚
    /// </summary>
    public double SliceThickness { get; set; }

    /// <summary>
    /// 窗宽
    /// </summary>
    public double WindowWidth { get; set; }

    /// <summary>
    /// 窗位
    /// </summary>
    public double WindowCenter { get; set; }
}

/// <summary>
/// 训练配置
/// </summary>
public class TrainingConfig
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; } = string.Empty;

    /// <summary>
    /// 数据集路径
    /// </summary>
    public string DatasetPath { get; set; } = string.Empty;

    /// <summary>
    /// 训练轮数
    /// </summary>
    public int Epochs { get; set; } = 100;

    /// <summary>
    /// 批次大小
    /// </summary>
    public int BatchSize { get; set; } = 16;

    /// <summary>
    /// 学习率
    /// </summary>
    public double LearningRate { get; set; } = 0.001;

    /// <summary>
    /// 图像大小
    /// </summary>
    public int ImageSize { get; set; } = 640;

    /// <summary>
    /// 是否使用预训练模型
    /// </summary>
    public bool UsePretrainedModel { get; set; } = true;

    /// <summary>
    /// 预训练模型路径
    /// </summary>
    public string PretrainedModelPath { get; set; } = string.Empty;

    /// <summary>
    /// 验证集比例
    /// </summary>
    public double ValidationSplit { get; set; } = 0.2;

    /// <summary>
    /// 是否启用数据增强
    /// </summary>
    public bool EnableDataAugmentation { get; set; } = true;

    /// <summary>
    /// 输出目录
    /// </summary>
    public string OutputDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 设备类型（CPU/GPU）
    /// </summary>
    public string Device { get; set; } = "auto";

    /// <summary>
    /// 工作线程数
    /// </summary>
    public int Workers { get; set; } = 4;
}

/// <summary>
/// 训练进度
/// </summary>
public class TrainingProgress
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public Guid TaskId { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 当前轮数
    /// </summary>
    public int CurrentEpoch { get; set; }

    /// <summary>
    /// 总轮数
    /// </summary>
    public int TotalEpochs { get; set; }

    /// <summary>
    /// 当前批次
    /// </summary>
    public int CurrentBatch { get; set; }

    /// <summary>
    /// 总批次数
    /// </summary>
    public int TotalBatches { get; set; }

    /// <summary>
    /// 训练损失
    /// </summary>
    public double TrainLoss { get; set; }

    /// <summary>
    /// 验证损失
    /// </summary>
    public double ValidationLoss { get; set; }

    /// <summary>
    /// 训练精度
    /// </summary>
    public double TrainAccuracy { get; set; }

    /// <summary>
    /// 验证精度
    /// </summary>
    public double ValidationAccuracy { get; set; }

    /// <summary>
    /// 学习率
    /// </summary>
    public double LearningRate { get; set; }

    /// <summary>
    /// 剩余时间（秒）
    /// </summary>
    public double EstimatedTimeRemaining { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 进度百分比
    /// </summary>
    public double ProgressPercentage => TotalEpochs > 0 ? (double)CurrentEpoch / TotalEpochs * 100 : 0;

    /// <summary>
    /// 是否完成
    /// </summary>
    public bool IsCompleted { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; } = DateTime.Now;
}

/// <summary>
/// 推理结果
/// </summary>
public class InferenceResult
{
    /// <summary>
    /// 结果ID
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 检测结果
    /// </summary>
    public List<YoloDetection> Detections { get; set; } = new();

    /// <summary>
    /// 推理时间（毫秒）
    /// </summary>
    public double InferenceTime { get; set; }

    /// <summary>
    /// 预处理时间（毫秒）
    /// </summary>
    public double PreprocessTime { get; set; }

    /// <summary>
    /// 后处理时间（毫秒）
    /// </summary>
    public double PostprocessTime { get; set; }

    /// <summary>
    /// 总时间（毫秒）
    /// </summary>
    public double TotalTime => PreprocessTime + InferenceTime + PostprocessTime;

    /// <summary>
    /// 模型信息
    /// </summary>
    public string ModelInfo { get; set; } = string.Empty;

    /// <summary>
    /// 输入图像尺寸
    /// </summary>
    public (int Width, int Height) InputSize { get; set; }

    /// <summary>
    /// 置信度阈值
    /// </summary>
    public double ConfidenceThreshold { get; set; }

    /// <summary>
    /// IoU阈值
    /// </summary>
    public double IoUThreshold { get; set; }

    /// <summary>
    /// 推理时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 系统状态
/// </summary>
public class SystemStatus
{
    /// <summary>
    /// CPU使用率（%）
    /// </summary>
    public double CpuUsage { get; set; }

    /// <summary>
    /// 内存使用率（%）
    /// </summary>
    public double MemoryUsage { get; set; }

    /// <summary>
    /// GPU使用率（%）
    /// </summary>
    public double GpuUsage { get; set; }

    /// <summary>
    /// GPU内存使用率（%）
    /// </summary>
    public double GpuMemoryUsage { get; set; }

    /// <summary>
    /// 磁盘使用率（%）
    /// </summary>
    public double DiskUsage { get; set; }

    /// <summary>
    /// 活跃任务数
    /// </summary>
    public int ActiveTasks { get; set; }

    /// <summary>
    /// 队列中的任务数
    /// </summary>
    public int QueuedTasks { get; set; }

    /// <summary>
    /// 系统运行时间
    /// </summary>
    public TimeSpan Uptime { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.Now;

    /// <summary>
    /// 服务状态
    /// </summary>
    public Dictionary<string, bool> ServiceStatus { get; set; } = new();
}
