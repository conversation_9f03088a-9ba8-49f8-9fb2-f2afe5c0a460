using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Models;
using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using System.Numerics;

namespace MedicalImageAnalysis.Infrastructure.Algorithms;

/// <summary>
/// 智能标注算法库
/// 提供基于AI的智能标注、质量评估和优化算法
/// </summary>
public class SmartAnnotationAlgorithms
{
    private readonly ILogger<SmartAnnotationAlgorithms> _logger;

    public SmartAnnotationAlgorithms(ILogger<SmartAnnotationAlgorithms> logger)
    {
        _logger = logger;
    }

    #region 智能目标检测

    /// <summary>
    /// 基于边缘检测的目标候选区域生成
    /// </summary>
    public async Task<List<AnnotationCandidate>> GenerateEdgeBasedCandidatesAsync(PixelData pixelData, EdgeDetectionConfig config)
    {
        _logger.LogInformation("生成基于边缘检测的标注候选区域");

        var candidates = new List<AnnotationCandidate>();
        var width = pixelData.Width;
        var height = pixelData.Height;
        var data = ConvertToFloatArray(pixelData);

        await Task.Run(() =>
        {
            // 1. 边缘检测
            var edges = ApplyCannyEdgeDetection(data, width, height, config.LowThreshold, config.HighThreshold);

            // 2. 形态学操作连接边缘
            var connected = ApplyMorphologicalClosing(edges, width, height, config.KernelSize);

            // 3. 轮廓检测
            var contours = FindContours(connected, width, height);

            // 4. 过滤和评分
            foreach (var contour in contours)
            {
                var boundingBox = GetBoundingBox(contour);
                var area = CalculateContourArea(contour);
                var perimeter = CalculateContourPerimeter(contour);
                
                // 过滤太小或太大的区域
                if (area < config.MinArea || area > config.MaxArea)
                    continue;

                // 计算形状特征
                var circularity = 4 * Math.PI * area / (perimeter * perimeter);
                var aspectRatio = (double)boundingBox.Width / boundingBox.Height;
                
                var candidate = new AnnotationCandidate
                {
                    BoundingBox = boundingBox,
                    Contour = contour,
                    Confidence = CalculateEdgeBasedConfidence(area, circularity, aspectRatio),
                    Features = new Dictionary<string, double>
                    {
                        ["area"] = area,
                        ["perimeter"] = perimeter,
                        ["circularity"] = circularity,
                        ["aspectRatio"] = aspectRatio
                    }
                };

                candidates.Add(candidate);
            }
        });

        // 按置信度排序
        candidates = candidates.OrderByDescending(c => c.Confidence).ToList();
        
        _logger.LogInformation("生成了 {Count} 个候选区域", candidates.Count);
        return candidates;
    }

    /// <summary>
    /// 基于区域生长的目标检测
    /// </summary>
    public async Task<List<AnnotationCandidate>> GenerateRegionGrowingCandidatesAsync(PixelData pixelData, RegionGrowingConfig config)
    {
        _logger.LogInformation("生成基于区域生长的标注候选区域");

        var candidates = new List<AnnotationCandidate>();
        var width = pixelData.Width;
        var height = pixelData.Height;
        var data = ConvertToFloatArray(pixelData);

        await Task.Run(() =>
        {
            var visited = new bool[width * height];
            var seedPoints = GenerateSeedPoints(data, width, height, config.SeedSpacing);

            foreach (var seed in seedPoints)
            {
                if (visited[seed.y * width + seed.x])
                    continue;

                var region = GrowRegion(data, width, height, seed, config.Threshold, visited);
                
                if (region.Count < config.MinRegionSize || region.Count > config.MaxRegionSize)
                    continue;

                var boundingBox = GetRegionBoundingBox(region);
                var meanIntensity = region.Average(p => data[p.y * width + p.x]);
                var stdIntensity = CalculateStandardDeviation(region.Select(p => data[p.y * width + p.x]));

                var candidate = new AnnotationCandidate
                {
                    BoundingBox = boundingBox,
                    Region = region,
                    Confidence = CalculateRegionBasedConfidence(region.Count, meanIntensity, stdIntensity),
                    Features = new Dictionary<string, double>
                    {
                        ["regionSize"] = region.Count,
                        ["meanIntensity"] = meanIntensity,
                        ["stdIntensity"] = stdIntensity,
                        ["compactness"] = CalculateCompactness(region, boundingBox)
                    }
                };

                candidates.Add(candidate);
            }
        });

        candidates = candidates.OrderByDescending(c => c.Confidence).ToList();
        
        _logger.LogInformation("生成了 {Count} 个候选区域", candidates.Count);
        return candidates;
    }

    #endregion

    #region 标注质量评估

    /// <summary>
    /// 评估标注的几何质量
    /// </summary>
    public async Task<GeometricQualityScore> EvaluateGeometricQualityAsync(Annotation annotation, PixelData pixelData)
    {
        _logger.LogInformation("评估标注几何质量，标注ID: {Id}", annotation.Id);

        var score = new GeometricQualityScore();

        await Task.Run(() =>
        {
            var boundingBox = annotation.BoundingBox;
            var area = boundingBox.Width * boundingBox.Height;
            var aspectRatio = boundingBox.Width / boundingBox.Height;

            // 1. 边界清晰度评估
            score.BoundaryClarity = EvaluateBoundaryClarity(pixelData, boundingBox);

            // 2. 形状规整度评估
            score.ShapeRegularity = EvaluateShapeRegularity(boundingBox, aspectRatio);

            // 3. 尺寸合理性评估
            score.SizeReasonableness = EvaluateSizeReasonableness(area, pixelData.Width * pixelData.Height);

            // 4. 位置合理性评估
            score.PositionReasonableness = EvaluatePositionReasonableness(boundingBox, pixelData.Width, pixelData.Height);

            // 5. 计算总体几何质量分数
            score.OverallScore = (score.BoundaryClarity + score.ShapeRegularity + 
                                score.SizeReasonableness + score.PositionReasonableness) / 4.0;
        });

        return score;
    }

    /// <summary>
    /// 评估标注的语义质量
    /// </summary>
    public async Task<SemanticQualityScore> EvaluateSemanticQualityAsync(Annotation annotation, PixelData pixelData, SemanticEvaluationConfig config)
    {
        _logger.LogInformation("评估标注语义质量，标注ID: {Id}", annotation.Id);

        var score = new SemanticQualityScore();

        await Task.Run(() =>
        {
            var boundingBox = annotation.BoundingBox;
            var regionData = ExtractRegionData(pixelData, boundingBox);

            // 1. 强度分布评估
            score.IntensityDistribution = EvaluateIntensityDistribution(regionData, config);

            // 2. 纹理一致性评估
            score.TextureConsistency = EvaluateTextureConsistency(regionData);

            // 3. 对比度评估
            score.Contrast = EvaluateRegionContrast(pixelData, boundingBox);

            // 4. 类别一致性评估（基于预定义的类别特征）
            score.CategoryConsistency = EvaluateCategoryConsistency(annotation.Label, regionData, config);

            // 5. 计算总体语义质量分数
            score.OverallScore = (score.IntensityDistribution + score.TextureConsistency + 
                                score.Contrast + score.CategoryConsistency) / 4.0;
        });

        return score;
    }

    /// <summary>
    /// 评估多个标注之间的一致性
    /// </summary>
    public async Task<ConsistencyScore> EvaluateAnnotationConsistencyAsync(List<Annotation> annotations, PixelData pixelData)
    {
        _logger.LogInformation("评估标注一致性，标注数量: {Count}", annotations.Count);

        var score = new ConsistencyScore();

        await Task.Run(() =>
        {
            if (annotations.Count < 2)
            {
                score.OverallScore = 1.0;
                return;
            }

            // 1. 尺寸一致性
            score.SizeConsistency = EvaluateSizeConsistency(annotations);

            // 2. 形状一致性
            score.ShapeConsistency = EvaluateShapeConsistency(annotations);

            // 3. 分布一致性
            score.DistributionConsistency = EvaluateDistributionConsistency(annotations, pixelData.Width, pixelData.Height);

            // 4. 标签一致性
            score.LabelConsistency = EvaluateLabelConsistency(annotations);

            // 5. 重叠检测
            score.OverlapScore = EvaluateOverlapScore(annotations);

            // 6. 计算总体一致性分数
            score.OverallScore = (score.SizeConsistency + score.ShapeConsistency + 
                                score.DistributionConsistency + score.LabelConsistency + score.OverlapScore) / 5.0;
        });

        return score;
    }

    #endregion

    #region 自适应优化

    /// <summary>
    /// 自适应边界优化
    /// </summary>
    public async Task<Annotation> OptimizeBoundaryAsync(Annotation annotation, PixelData pixelData, BoundaryOptimizationConfig config)
    {
        _logger.LogInformation("优化标注边界，标注ID: {Id}", annotation.Id);

        var optimizedAnnotation = annotation.Clone();

        await Task.Run(() =>
        {
            var boundingBox = annotation.BoundingBox;
            var regionData = ExtractRegionData(pixelData, boundingBox);

            // 1. 基于梯度的边界调整
            if (config.UseGradientOptimization)
            {
                optimizedAnnotation.BoundingBox = OptimizeBoundaryWithGradient(pixelData, boundingBox, config.GradientThreshold);
            }

            // 2. 基于活动轮廓的边界优化
            if (config.UseActiveContour)
            {
                optimizedAnnotation.BoundingBox = OptimizeBoundaryWithActiveContour(pixelData, boundingBox, config.ActiveContourConfig);
            }

            // 3. 基于区域生长的边界调整
            if (config.UseRegionGrowing)
            {
                optimizedAnnotation.BoundingBox = OptimizeBoundaryWithRegionGrowing(pixelData, boundingBox, config.RegionGrowingThreshold);
            }

            // 4. 更新置信度
            optimizedAnnotation.Confidence = CalculateOptimizedConfidence(optimizedAnnotation, pixelData);
        });

        return optimizedAnnotation;
    }

    /// <summary>
    /// 智能标注推荐
    /// </summary>
    public async Task<List<AnnotationRecommendation>> GenerateSmartRecommendationsAsync(PixelData pixelData, List<Annotation> existingAnnotations, RecommendationConfig config)
    {
        _logger.LogInformation("生成智能标注推荐");

        var recommendations = new List<AnnotationRecommendation>();

        await Task.Run(() =>
        {
            // 1. 基于相似性的推荐
            if (config.UseSimilarityRecommendation && existingAnnotations.Any())
            {
                var similarityRecommendations = GenerateSimilarityBasedRecommendations(pixelData, existingAnnotations, config);
                recommendations.AddRange(similarityRecommendations);
            }

            // 2. 基于模式识别的推荐
            if (config.UsePatternRecognition)
            {
                var patternRecommendations = GeneratePatternBasedRecommendations(pixelData, config);
                recommendations.AddRange(patternRecommendations);
            }

            // 3. 基于异常检测的推荐
            if (config.UseAnomalyDetection)
            {
                var anomalyRecommendations = GenerateAnomalyBasedRecommendations(pixelData, existingAnnotations, config);
                recommendations.AddRange(anomalyRecommendations);
            }

            // 4. 基于上下文的推荐
            if (config.UseContextualRecommendation)
            {
                var contextualRecommendations = GenerateContextualRecommendations(pixelData, existingAnnotations, config);
                recommendations.AddRange(contextualRecommendations);
            }
        });

        // 按置信度排序并限制数量
        recommendations = recommendations
            .OrderByDescending(r => r.Confidence)
            .Take(config.MaxRecommendations)
            .ToList();

        _logger.LogInformation("生成了 {Count} 个推荐", recommendations.Count);
        return recommendations;
    }

    #endregion

    #region 辅助方法

    private float[] ConvertToFloatArray(PixelData pixelData)
    {
        var data = pixelData.Data;
        var length = pixelData.Width * pixelData.Height;
        var result = new float[length];

        if (data is float[] floatArray)
        {
            Array.Copy(floatArray, result, length);
        }
        else if (data is byte[] byteArray)
        {
            for (int i = 0; i < length; i++)
                result[i] = byteArray[i] / 255.0f;
        }
        else if (data is ushort[] ushortArray)
        {
            var max = ushortArray.Max();
            for (int i = 0; i < length; i++)
                result[i] = ushortArray[i] / (float)max;
        }

        return result;
    }

    private byte[] ApplyCannyEdgeDetection(float[] data, int width, int height, double lowThreshold, double highThreshold)
    {
        // 简化的Canny边缘检测实现
        var edges = new byte[width * height];
        
        // 这里应该实现完整的Canny算法
        // 为了简化，使用基本的梯度检测
        for (int y = 1; y < height - 1; y++)
        {
            for (int x = 1; x < width - 1; x++)
            {
                var idx = y * width + x;
                var gx = data[idx + 1] - data[idx - 1];
                var gy = data[(y + 1) * width + x] - data[(y - 1) * width + x];
                var magnitude = Math.Sqrt(gx * gx + gy * gy);
                
                edges[idx] = magnitude > highThreshold ? (byte)255 : (byte)0;
            }
        }
        
        return edges;
    }

    private double CalculateEdgeBasedConfidence(double area, double circularity, double aspectRatio)
    {
        // 基于形状特征计算置信度
        var areaScore = Math.Min(1.0, area / 10000.0); // 归一化面积分数
        var circularityScore = circularity; // 圆形度分数
        var aspectScore = 1.0 / (1.0 + Math.Abs(aspectRatio - 1.0)); // 接近正方形的分数
        
        return (areaScore + circularityScore + aspectScore) / 3.0;
    }

    private double CalculateRegionBasedConfidence(int regionSize, double meanIntensity, double stdIntensity)
    {
        // 基于区域特征计算置信度
        var sizeScore = Math.Min(1.0, regionSize / 5000.0);
        var intensityScore = meanIntensity / 255.0;
        var uniformityScore = 1.0 / (1.0 + stdIntensity / 50.0);
        
        return (sizeScore + intensityScore + uniformityScore) / 3.0;
    }

    private double EvaluateBoundaryClarity(PixelData pixelData, Rectangle boundingBox)
    {
        // 评估边界清晰度
        // 计算边界处的梯度强度
        return 0.8; // 简化实现
    }

    private double EvaluateShapeRegularity(Rectangle boundingBox, double aspectRatio)
    {
        // 评估形状规整度
        return 1.0 / (1.0 + Math.Abs(aspectRatio - 1.0));
    }

    private double EvaluateSizeReasonableness(double area, double totalArea)
    {
        // 评估尺寸合理性
        var ratio = area / totalArea;
        return ratio > 0.001 && ratio < 0.5 ? 1.0 : 0.5;
    }

    private double EvaluatePositionReasonableness(Rectangle boundingBox, int imageWidth, int imageHeight)
    {
        // 评估位置合理性
        var centerX = boundingBox.X + boundingBox.Width / 2.0;
        var centerY = boundingBox.Y + boundingBox.Height / 2.0;
        
        var distanceFromCenter = Math.Sqrt(
            Math.Pow(centerX - imageWidth / 2.0, 2) + 
            Math.Pow(centerY - imageHeight / 2.0, 2));
        
        var maxDistance = Math.Sqrt(imageWidth * imageWidth + imageHeight * imageHeight) / 2.0;
        
        return 1.0 - (distanceFromCenter / maxDistance);
    }

    private double CalculateStandardDeviation(IEnumerable<float> values)
    {
        var mean = values.Average();
        return Math.Sqrt(values.Average(v => (v - mean) * (v - mean)));
    }

    private byte[] ApplyMorphologicalClosing(byte[] edges, int width, int height, int kernelSize)
    {
        // 简化的形态学闭运算
        var result = new byte[width * height];
        Array.Copy(edges, result, edges.Length);
        return result;
    }

    private List<List<Point>> FindContours(byte[] binary, int width, int height)
    {
        // 简化的轮廓检测
        var contours = new List<List<Point>>();
        var visited = new bool[width * height];

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                var idx = y * width + x;
                if (binary[idx] > 0 && !visited[idx])
                {
                    var contour = TraceContour(binary, width, height, x, y, visited);
                    if (contour.Count > 10) // 过滤太小的轮廓
                    {
                        contours.Add(contour);
                    }
                }
            }
        }

        return contours;
    }

    private List<Point> TraceContour(byte[] binary, int width, int height, int startX, int startY, bool[] visited)
    {
        var contour = new List<Point>();
        var stack = new Stack<Point>();
        stack.Push(new Point(startX, startY));

        while (stack.Count > 0)
        {
            var current = stack.Pop();
            var idx = current.Y * width + current.X;

            if (current.X < 0 || current.X >= width || current.Y < 0 || current.Y >= height ||
                visited[idx] || binary[idx] == 0)
                continue;

            visited[idx] = true;
            contour.Add(current);

            // 8连通邻域
            for (int dy = -1; dy <= 1; dy++)
            {
                for (int dx = -1; dx <= 1; dx++)
                {
                    if (dx == 0 && dy == 0) continue;
                    stack.Push(new Point(current.X + dx, current.Y + dy));
                }
            }
        }

        return contour;
    }

    private Rectangle GetBoundingBox(List<Point> contour)
    {
        if (!contour.Any()) return Rectangle.Empty;

        var minX = contour.Min(p => p.X);
        var maxX = contour.Max(p => p.X);
        var minY = contour.Min(p => p.Y);
        var maxY = contour.Max(p => p.Y);

        return new Rectangle(minX, minY, maxX - minX, maxY - minY);
    }

    private double CalculateContourArea(List<Point> contour)
    {
        if (contour.Count < 3) return 0;

        double area = 0;
        for (int i = 0; i < contour.Count; i++)
        {
            var j = (i + 1) % contour.Count;
            area += contour[i].X * contour[j].Y;
            area -= contour[j].X * contour[i].Y;
        }
        return Math.Abs(area) / 2.0;
    }

    private double CalculateContourPerimeter(List<Point> contour)
    {
        if (contour.Count < 2) return 0;

        double perimeter = 0;
        for (int i = 0; i < contour.Count; i++)
        {
            var j = (i + 1) % contour.Count;
            var dx = contour[j].X - contour[i].X;
            var dy = contour[j].Y - contour[i].Y;
            perimeter += Math.Sqrt(dx * dx + dy * dy);
        }
        return perimeter;
    }

    private List<Point> GenerateSeedPoints(float[] data, int width, int height, int spacing)
    {
        var seeds = new List<Point>();
        for (int y = spacing; y < height - spacing; y += spacing)
        {
            for (int x = spacing; x < width - spacing; x += spacing)
            {
                seeds.Add(new Point(x, y));
            }
        }
        return seeds;
    }

    private List<Point> GrowRegion(float[] data, int width, int height, Point seed, double threshold, bool[] visited)
    {
        var region = new List<Point>();
        var queue = new Queue<Point>();
        queue.Enqueue(seed);

        var seedValue = data[seed.Y * width + seed.X];

        while (queue.Count > 0)
        {
            var current = queue.Dequeue();
            var idx = current.Y * width + current.X;

            if (current.X < 0 || current.X >= width || current.Y < 0 || current.Y >= height ||
                visited[idx])
                continue;

            var currentValue = data[idx];
            if (Math.Abs(currentValue - seedValue) > threshold)
                continue;

            visited[idx] = true;
            region.Add(current);

            // 4连通邻域
            queue.Enqueue(new Point(current.X + 1, current.Y));
            queue.Enqueue(new Point(current.X - 1, current.Y));
            queue.Enqueue(new Point(current.X, current.Y + 1));
            queue.Enqueue(new Point(current.X, current.Y - 1));
        }

        return region;
    }

    private Rectangle GetRegionBoundingBox(List<Point> region)
    {
        return GetBoundingBox(region);
    }

    private double CalculateCompactness(List<Point> region, Rectangle boundingBox)
    {
        var area = region.Count;
        var boundingArea = boundingBox.Width * boundingBox.Height;
        return boundingArea > 0 ? (double)area / boundingArea : 0;
    }

    // 占位符方法，需要完整实现
    private float[] ExtractRegionData(PixelData pixelData, Rectangle boundingBox) => new float[0];
    private double EvaluateIntensityDistribution(float[] regionData, SemanticEvaluationConfig config) => 0.8;
    private double EvaluateTextureConsistency(float[] regionData) => 0.8;
    private double EvaluateRegionContrast(PixelData pixelData, Rectangle boundingBox) => 0.8;
    private double EvaluateCategoryConsistency(string label, float[] regionData, SemanticEvaluationConfig config) => 0.8;
    private double EvaluateSizeConsistency(List<Annotation> annotations) => 0.8;
    private double EvaluateShapeConsistency(List<Annotation> annotations) => 0.8;
    private double EvaluateDistributionConsistency(List<Annotation> annotations, int width, int height) => 0.8;
    private double EvaluateLabelConsistency(List<Annotation> annotations) => 0.8;
    private double EvaluateOverlapScore(List<Annotation> annotations) => 0.8;
    private Rectangle OptimizeBoundaryWithGradient(PixelData pixelData, Rectangle boundingBox, double threshold) => boundingBox;
    private Rectangle OptimizeBoundaryWithActiveContour(PixelData pixelData, Rectangle boundingBox, ActiveContourConfig config) => boundingBox;
    private Rectangle OptimizeBoundaryWithRegionGrowing(PixelData pixelData, Rectangle boundingBox, double threshold) => boundingBox;
    private double CalculateOptimizedConfidence(Annotation annotation, PixelData pixelData) => 0.8;
    private List<AnnotationRecommendation> GenerateSimilarityBasedRecommendations(PixelData pixelData, List<Annotation> annotations, RecommendationConfig config) => new();
    private List<AnnotationRecommendation> GeneratePatternBasedRecommendations(PixelData pixelData, RecommendationConfig config) => new();
    private List<AnnotationRecommendation> GenerateAnomalyBasedRecommendations(PixelData pixelData, List<Annotation> annotations, RecommendationConfig config) => new();
    private List<AnnotationRecommendation> GenerateContextualRecommendations(PixelData pixelData, List<Annotation> annotations, RecommendationConfig config) => new();

    #endregion
}

#region 数据模型

/// <summary>
/// 标注候选区域
/// </summary>
public class AnnotationCandidate
{
    public Rectangle BoundingBox { get; set; }
    public List<Point> Contour { get; set; } = new();
    public List<Point> Region { get; set; } = new();
    public double Confidence { get; set; }
    public Dictionary<string, double> Features { get; set; } = new();
    public string SuggestedLabel { get; set; } = string.Empty;
}

/// <summary>
/// 边缘检测配置
/// </summary>
public class EdgeDetectionConfig
{
    public double LowThreshold { get; set; } = 0.1;
    public double HighThreshold { get; set; } = 0.3;
    public int KernelSize { get; set; } = 3;
    public double MinArea { get; set; } = 100;
    public double MaxArea { get; set; } = 50000;
}

/// <summary>
/// 区域生长配置
/// </summary>
public class RegionGrowingConfig
{
    public double Threshold { get; set; } = 10.0;
    public int SeedSpacing { get; set; } = 20;
    public int MinRegionSize { get; set; } = 50;
    public int MaxRegionSize { get; set; } = 10000;
}

/// <summary>
/// 几何质量分数
/// </summary>
public class GeometricQualityScore
{
    public double BoundaryClarity { get; set; }
    public double ShapeRegularity { get; set; }
    public double SizeReasonableness { get; set; }
    public double PositionReasonableness { get; set; }
    public double OverallScore { get; set; }
}

/// <summary>
/// 语义质量分数
/// </summary>
public class SemanticQualityScore
{
    public double IntensityDistribution { get; set; }
    public double TextureConsistency { get; set; }
    public double Contrast { get; set; }
    public double CategoryConsistency { get; set; }
    public double OverallScore { get; set; }
}

/// <summary>
/// 一致性分数
/// </summary>
public class ConsistencyScore
{
    public double SizeConsistency { get; set; }
    public double ShapeConsistency { get; set; }
    public double DistributionConsistency { get; set; }
    public double LabelConsistency { get; set; }
    public double OverlapScore { get; set; }
    public double OverallScore { get; set; }
}

/// <summary>
/// 语义评估配置
/// </summary>
public class SemanticEvaluationConfig
{
    public Dictionary<string, IntensityRange> CategoryIntensityRanges { get; set; } = new();
    public Dictionary<string, TextureProfile> CategoryTextureProfiles { get; set; } = new();
}

/// <summary>
/// 强度范围
/// </summary>
public class IntensityRange
{
    public double Min { get; set; }
    public double Max { get; set; }
    public double Mean { get; set; }
    public double StandardDeviation { get; set; }
}

/// <summary>
/// 纹理轮廓
/// </summary>
public class TextureProfile
{
    public double Contrast { get; set; }
    public double Correlation { get; set; }
    public double Energy { get; set; }
    public double Homogeneity { get; set; }
}

/// <summary>
/// 边界优化配置
/// </summary>
public class BoundaryOptimizationConfig
{
    public bool UseGradientOptimization { get; set; } = true;
    public bool UseActiveContour { get; set; } = true;
    public bool UseRegionGrowing { get; set; } = true;
    public double GradientThreshold { get; set; } = 0.1;
    public double RegionGrowingThreshold { get; set; } = 10.0;
    public ActiveContourConfig ActiveContourConfig { get; set; } = new();
}

/// <summary>
/// 活动轮廓配置
/// </summary>
public class ActiveContourConfig
{
    public int MaxIterations { get; set; } = 100;
    public double Alpha { get; set; } = 0.1;
    public double Beta { get; set; } = 0.1;
    public double Gamma { get; set; } = 0.1;
}

/// <summary>
/// 推荐配置
/// </summary>
public class RecommendationConfig
{
    public bool UseSimilarityRecommendation { get; set; } = true;
    public bool UsePatternRecognition { get; set; } = true;
    public bool UseAnomalyDetection { get; set; } = true;
    public bool UseContextualRecommendation { get; set; } = true;
    public int MaxRecommendations { get; set; } = 10;
    public double SimilarityThreshold { get; set; } = 0.7;
}

/// <summary>
/// 标注推荐
/// </summary>
public class AnnotationRecommendation
{
    public Rectangle BoundingBox { get; set; }
    public string SuggestedLabel { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public string RecommendationType { get; set; } = string.Empty;
    public string Reasoning { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

#endregion
