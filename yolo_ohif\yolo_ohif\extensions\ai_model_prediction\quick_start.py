#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构后AI预测扩展快速开始脚本

这个脚本演示如何快速使用重构后的AI预测扩展
使用方法：
    python quick_start.py
"""

import sys
import os
from pathlib import Path

# 设置路径
current_dir = Path(__file__).parent.absolute()
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

def demo_basic_usage():
    """演示基本使用方法"""
    print("=== 基本使用演示 ===")
    
    try:
        # 方法1: 使用工厂创建默认扩展
        from extensions.ai_model_prediction.factory import ExtensionFactory
        
        print("1. 创建默认扩展...")
        extension = ExtensionFactory.create_default()
        
        # 获取扩展信息
        info = extension.get_extension_info()
        print(f"   扩展名称: {info['name']}")
        print(f"   扩展版本: {info['version']}")
        print(f"   初始化状态: {info['initialized']}")
        
        # 获取可用模型
        models = extension.get_available_models()
        print(f"   可用模型数量: {len(models)}")
        
        if models:
            for i, model in enumerate(models):
                print(f"   模型{i+1}: {model.get('name', 'Unknown')} (ID: {model.get('id', 'Unknown')})")
        
        # 清理
        extension.cleanup()
        print("   ✓ 基本使用演示完成")
        
    except Exception as e:
        print(f"   ✗ 基本使用演示失败: {e}")

def demo_builder_pattern():
    """演示构建器模式"""
    print("\n=== 构建器模式演示 ===")
    
    try:
        from extensions.ai_model_prediction.factory import create_builder
        
        print("2. 使用构建器创建自定义扩展...")
        extension = (create_builder()
                    .with_name('演示AI扩展')
                    .with_version('1.0.0')
                    .add_model({
                        'id': 'demo_model',
                        'name': '演示检测模型',
                        'type': 'detection',
                        'modality': 'CT',
                        'endpoint': 'http://localhost:8000/predict',
                        'classes': ['normal', 'abnormal'],
                        'confidence_threshold': 0.7,
                        'enabled': True
                    })
                    .enable_cache(ttl=300)
                    .enable_debug()
                    .build())
        
        # 获取信息
        info = extension.get_extension_info()
        print(f"   扩展名称: {info['name']}")
        print(f"   模型数量: {info['models_count']}")
        
        # 设置活动模型
        models = extension.get_available_models()
        if models:
            model_id = models[0]['id']
            success = extension.set_active_model(model_id)
            print(f"   设置活动模型: {'成功' if success else '失败'}")
            
            active_model = extension.get_active_model()
            if active_model:
                print(f"   当前活动模型: {active_model['name']}")
        
        # 清理
        extension.cleanup()
        print("   ✓ 构建器模式演示完成")
        
    except Exception as e:
        print(f"   ✗ 构建器模式演示失败: {e}")

def demo_event_system():
    """演示事件系统"""
    print("\n=== 事件系统演示 ===")
    
    try:
        from extensions.ai_model_prediction.factory import create_extension
        from extensions.ai_model_prediction.core.interfaces import EventManagerInterface
        
        print("3. 演示事件系统...")
        extension = create_extension()
        
        # 获取事件服务
        event_service = extension.container.get_service(EventManagerInterface)
        
        # 定义事件处理器
        events_received = []
        
        def on_model_changed(event_data):
            events_received.append(f"模型切换: {event_data.data.get('model_id')}")
            print(f"   📢 事件: 模型已切换到 {event_data.data.get('model_id')}")
        
        def on_prediction_completed(event_data):
            count = event_data.data.get('detections_count', 0)
            time = event_data.data.get('processing_time', 0)
            events_received.append(f"预测完成: {count}个检测，耗时{time}秒")
            print(f"   📢 事件: 预测完成，检测到{count}个目标，耗时{time:.2f}秒")
        
        # 订阅事件
        sub1 = event_service.subscribe('model_changed', on_model_changed)
        sub2 = event_service.subscribe('prediction_completed', on_prediction_completed)
        
        # 触发事件
        print("   触发测试事件...")
        event_service.publish('model_changed', {
            'model_id': 'test_model_1',
            'timestamp': '2024-01-01T00:00:00'
        })
        
        event_service.publish('prediction_completed', {
            'model_id': 'test_model_1',
            'detections_count': 3,
            'processing_time': 1.25
        })
        
        # 获取统计
        stats = event_service.get_stats()
        print(f"   事件统计: 发布{stats.get('events_published', 0)}个，订阅{stats.get('active_subscriptions', 0)}个")
        print(f"   接收到{len(events_received)}个事件")
        
        # 取消订阅
        event_service.unsubscribe(sub1)
        event_service.unsubscribe(sub2)
        
        # 清理
        extension.cleanup()
        print("   ✓ 事件系统演示完成")
        
    except Exception as e:
        print(f"   ✗ 事件系统演示失败: {e}")

def demo_cache_system():
    """演示缓存系统"""
    print("\n=== 缓存系统演示 ===")
    
    try:
        from extensions.ai_model_prediction.factory import create_builder
        from extensions.ai_model_prediction.core.interfaces import CacheManagerInterface
        
        print("4. 演示缓存系统...")
        extension = (create_builder()
                    .enable_cache(ttl=60)
                    .build())
        
        # 获取缓存服务
        cache_service = extension.container.get_service(CacheManagerInterface)
        
        # 缓存操作
        print("   执行缓存操作...")
        
        # 设置缓存
        cache_service.set('user_preference', {'theme': 'dark', 'language': 'zh-CN'})
        cache_service.set('model_config', {'confidence': 0.8, 'nms_threshold': 0.5})
        
        # 获取缓存
        user_pref = cache_service.get('user_preference')
        model_conf = cache_service.get('model_config')
        
        print(f"   用户偏好: {user_pref}")
        print(f"   模型配置: {model_conf}")
        
        # 检查键存在
        exists = cache_service.exists('user_preference')
        print(f"   用户偏好键存在: {exists}")
        
        # 获取所有键
        keys = cache_service.keys()
        print(f"   缓存键列表: {keys}")
        
        # 获取统计
        stats = cache_service.get_stats()
        print(f"   缓存统计: {stats}")
        
        # 清理
        cache_service.clear()
        print("   缓存已清理")
        
        extension.cleanup()
        print("   ✓ 缓存系统演示完成")
        
    except Exception as e:
        print(f"   ✗ 缓存系统演示失败: {e}")

def demo_service_container():
    """演示服务容器"""
    print("\n=== 服务容器演示 ===")
    
    try:
        from extensions.ai_model_prediction.core.container import ServiceContainer, ServiceLifetime
        from extensions.ai_model_prediction.core.interfaces import CacheManagerInterface, EventManagerInterface
        from extensions.ai_model_prediction.services.cache_service import CacheService
        from extensions.ai_model_prediction.services.event_service import EventService
        
        print("5. 演示服务容器...")
        
        # 创建容器
        container = ServiceContainer()
        
        # 注册服务
        container.register_singleton(CacheManagerInterface, CacheService)
        container.register_singleton(EventManagerInterface, EventService)
        
        print(f"   已注册{len(container.get_registered_services())}个服务")
        
        # 获取服务
        cache_service = container.get_service(CacheManagerInterface)
        event_service = container.get_service(EventManagerInterface)
        
        print(f"   缓存服务类型: {type(cache_service).__name__}")
        print(f"   事件服务类型: {type(event_service).__name__}")
        
        # 测试单例
        cache_service2 = container.get_service(CacheManagerInterface)
        is_singleton = cache_service is cache_service2
        print(f"   单例模式验证: {'✓' if is_singleton else '✗'}")
        
        # 获取容器统计
        stats = container.get_stats()
        print(f"   容器统计: {stats}")
        
        # 清理
        container.clear()
        print("   ✓ 服务容器演示完成")
        
    except Exception as e:
        print(f"   ✗ 服务容器演示失败: {e}")

def demo_error_handling():
    """演示错误处理"""
    print("\n=== 错误处理演示 ===")
    
    try:
        from extensions.ai_model_prediction.factory import ExtensionFactory
        from extensions.ai_model_prediction.core.exceptions import ExtensionError, ModelNotFoundError
        
        print("6. 演示错误处理...")
        
        # 创建最小扩展
        extension = ExtensionFactory.create_minimal()
        
        # 测试模型不存在错误
        print("   测试设置不存在的模型...")
        try:
            success = extension.set_active_model('nonexistent_model')
            print(f"   设置结果: {'成功' if success else '失败（预期）'}")
        except ModelNotFoundError as e:
            print(f"   ✓ 捕获到预期的模型未找到错误: {e}")
        except Exception as e:
            print(f"   捕获到其他错误: {e}")
        
        # 测试预测错误
        print("   测试无模型预测...")
        try:
            result = extension.run_prediction({'test': 'data'})
            if result is None:
                print("   ✓ 预测返回None（预期行为）")
            else:
                print(f"   预测返回: {result}")
        except Exception as e:
            print(f"   ✓ 捕获到预期的预测错误: {e}")
        
        # 测试配置错误
        print("   测试无效配置...")
        try:
            ExtensionFactory.create_from_file('nonexistent_config.json')
        except ExtensionError as e:
            print(f"   ✓ 捕获到预期的配置错误: {e}")
        except Exception as e:
            print(f"   捕获到其他错误: {e}")
        
        # 清理
        extension.cleanup()
        print("   ✓ 错误处理演示完成")
        
    except Exception as e:
        print(f"   ✗ 错误处理演示失败: {e}")

def main():
    """主函数"""
    print("🚀 重构后AI预测扩展快速开始")
    print("=" * 50)
    print("这个脚本将演示重构后扩展的主要功能")
    print()
    
    try:
        # 运行所有演示
        demo_basic_usage()
        demo_builder_pattern()
        demo_event_system()
        demo_cache_system()
        demo_service_container()
        demo_error_handling()
        
        print("\n" + "=" * 50)
        print("🎉 快速开始演示完成！")
        print()
        print("接下来你可以：")
        print("1. 运行完整测试: python run_tests.py")
        print("2. 查看使用示例: python example_usage.py")
        print("3. 阅读详细文档: README.md")
        print("4. 开始集成到你的项目中")
        
    except Exception as e:
        print(f"\n❌ 快速开始演示失败: {e}")
        print("请检查所有必要的文件是否已创建")
        return 1
    
    return 0

if __name__ == '__main__':
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n演示被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n快速开始脚本发生错误: {e}")
        sys.exit(1)