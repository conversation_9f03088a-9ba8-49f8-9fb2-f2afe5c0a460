using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.WpfClient.Models;
using MedicalImageAnalysis.WpfClient.Services;
using System.Collections.ObjectModel;
using System.Windows.Media.Imaging;
using System.Windows;

namespace MedicalImageAnalysis.WpfClient.ViewModels;

/// <summary>
/// 智能标注ViewModel
/// </summary>
public partial class AnnotationViewModel : ObservableObject
{
    private readonly ILogger<AnnotationViewModel> _logger;
    private readonly IApiService _apiService;
    private readonly IDialogService _dialogService;
    private readonly INotificationService _notificationService;
    private readonly IYoloInferenceService _yoloInferenceService;
    private readonly IImageProcessingService _imageProcessingService;

    [ObservableProperty]
    private ObservableCollection<Models.FileInfo> _imageFiles = new();

    [ObservableProperty]
    private Models.FileInfo? _selectedImageFile;

    [ObservableProperty]
    private BitmapImage? _currentImage;

    [ObservableProperty]
    private bool _isLoading = false;

    [ObservableProperty]
    private string _statusMessage = "就绪";

    [ObservableProperty]
    private double _progressValue = 0;

    [ObservableProperty]
    private bool _isProgressVisible = false;

    [ObservableProperty]
    private ObservableCollection<AnnotationInfo> _annotations = new();

    [ObservableProperty]
    private AnnotationInfo? _selectedAnnotation;

    [ObservableProperty]
    private string _selectedTool = "Rectangle";

    [ObservableProperty]
    private bool _isAnnotationMode = false;

    [ObservableProperty]
    private double _confidence = 0.5;

    [ObservableProperty]
    private string _selectedModel = "YOLOv11";

    [ObservableProperty]
    private ObservableCollection<string> _availableModels = new() { "YOLOv11", "YOLOv8", "Custom" };

    [ObservableProperty]
    private ObservableCollection<string> _availableClasses = new() { "肿瘤", "病变", "异常区域", "正常组织" };

    [ObservableProperty]
    private string _selectedClass = "肿瘤";

    [ObservableProperty]
    private bool _showPredictions = true;

    [ObservableProperty]
    private bool _showGroundTruth = true;

    [ObservableProperty]
    private double _zoomLevel = 1.0;

    [ObservableProperty]
    private Point _imageOffset = new Point(0, 0);

    public ObservableCollection<string> AnnotationTools { get; } = new()
    {
        "Rectangle", "Circle", "Polygon", "Freehand", "Point"
    };

    public AnnotationViewModel(
        ILogger<AnnotationViewModel> logger,
        IApiService apiService,
        IDialogService dialogService,
        INotificationService notificationService,
        IYoloInferenceService yoloInferenceService,
        IImageProcessingService imageProcessingService)
    {
        _logger = logger;
        _apiService = apiService;
        _dialogService = dialogService;
        _notificationService = notificationService;
        _yoloInferenceService = yoloInferenceService;
        _imageProcessingService = imageProcessingService;

        // 监听选中文件变化
        PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(SelectedImageFile))
            {
                _ = LoadImageAsync();
            }
        };
    }

    /// <summary>
    /// 打开图像文件
    /// </summary>
    [RelayCommand]
    private async Task OpenImage()
    {
        try
        {
            var filePath = await _dialogService.OpenFileDialogAsync(
                "选择图像文件",
                "图像文件 (*.jpg;*.jpeg;*.png;*.bmp;*.dcm)|*.jpg;*.jpeg;*.png;*.bmp;*.dcm|所有文件 (*.*)|*.*");

            if (!string.IsNullOrEmpty(filePath))
            {
                await LoadImageFromPathAsync(filePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开图像文件失败");
            await _notificationService.ShowErrorAsync("错误", "打开图像文件失败");
        }
    }

    /// <summary>
    /// 运行AI推理
    /// </summary>
    [RelayCommand]
    private async Task RunInference()
    {
        try
        {
            if (SelectedImageFile == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择图像文件");
                return;
            }

            IsLoading = true;
            IsProgressVisible = true;
            ProgressValue = 0;
            StatusMessage = "正在运行AI推理...";

            try
            {
                // 配置YOLO推理参数
                var config = new YoloInferenceConfig
                {
                    ModelPath = SelectedModel == "Custom" ? "custom_model.pt" : $"{SelectedModel.ToLower()}.pt",
                    Confidence = Confidence,
                    IoU = 0.45
                };

                // 运行YOLO推理
                ProgressValue = 30;
                var detections = await _yoloInferenceService.DetectAsync(SelectedImageFile.Path, config);

                ProgressValue = 70;

                // 转换检测结果为标注
                Annotations.Clear();
                foreach (var detection in detections)
                {
                    var annotation = new AnnotationInfo
                    {
                        Id = Guid.NewGuid(),
                        Label = detection.Label,
                        Confidence = detection.Confidence,
                        BoundingBox = new System.Windows.Rect(detection.X, detection.Y, detection.Width, detection.Height),
                        Type = "Detection",
                        CreatedTime = DateTime.Now
                    };

                    Annotations.Add(annotation);
                }

                ProgressValue = 100;
                StatusMessage = $"AI推理完成，检测到 {Annotations.Count} 个目标";
                await _notificationService.ShowSuccessAsync("成功", $"AI推理完成，检测到 {Annotations.Count} 个目标");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "YOLO推理过程中发生错误");

                // 如果推理失败，生成模拟结果
                await GenerateMockAnnotations();
                StatusMessage = $"推理失败，显示模拟结果: {Annotations.Count} 个目标";
                await _notificationService.ShowWarningAsync("警告", "AI推理失败，显示模拟结果");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI推理失败");
            await _notificationService.ShowErrorAsync("错误", "AI推理失败");
        }
        finally
        {
            IsLoading = false;
            IsProgressVisible = false;
        }
    }

    /// <summary>
    /// 保存标注
    /// </summary>
    [RelayCommand]
    private async Task SaveAnnotations()
    {
        try
        {
            if (Annotations.Count == 0)
            {
                await _notificationService.ShowWarningAsync("警告", "没有可保存的标注");
                return;
            }

            var filePath = await _dialogService.SaveFileDialogAsync(
                "保存标注文件",
                "JSON文件 (*.json)|*.json|XML文件 (*.xml)|*.xml");

            if (!string.IsNullOrEmpty(filePath))
            {
                // 这里应该实现标注保存逻辑
                StatusMessage = $"标注已保存到: {filePath}";
                await _notificationService.ShowSuccessAsync("成功", "标注保存成功");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存标注失败");
            await _notificationService.ShowErrorAsync("错误", "保存标注失败");
        }
    }

    /// <summary>
    /// 加载标注
    /// </summary>
    [RelayCommand]
    private async Task LoadAnnotations()
    {
        try
        {
            var filePath = await _dialogService.OpenFileDialogAsync(
                "加载标注文件",
                "JSON文件 (*.json)|*.json|XML文件 (*.xml)|*.xml");

            if (!string.IsNullOrEmpty(filePath))
            {
                // 这里应该实现标注加载逻辑
                StatusMessage = $"标注已从 {filePath} 加载";
                await _notificationService.ShowSuccessAsync("成功", "标注加载成功");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载标注失败");
            await _notificationService.ShowErrorAsync("错误", "加载标注失败");
        }
    }

    /// <summary>
    /// 清除所有标注
    /// </summary>
    [RelayCommand]
    private async Task ClearAnnotations()
    {
        try
        {
            if (Annotations.Count == 0)
            {
                await _notificationService.ShowInfoAsync("信息", "没有标注需要清除");
                return;
            }

            var confirmed = await _dialogService.ShowConfirmationAsync(
                "确认", $"确定要清除所有 {Annotations.Count} 个标注吗？");

            if (confirmed)
            {
                Annotations.Clear();
                SelectedAnnotation = null;
                StatusMessage = "所有标注已清除";
                await _notificationService.ShowInfoAsync("信息", "所有标注已清除");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除标注失败");
            await _notificationService.ShowErrorAsync("错误", "清除标注失败");
        }
    }

    /// <summary>
    /// 删除选中标注
    /// </summary>
    [RelayCommand]
    private async Task DeleteSelectedAnnotation()
    {
        try
        {
            if (SelectedAnnotation == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择要删除的标注");
                return;
            }

            var confirmed = await _dialogService.ShowConfirmationAsync(
                "确认", "确定要删除选中的标注吗？");

            if (confirmed)
            {
                Annotations.Remove(SelectedAnnotation);
                SelectedAnnotation = null;
                StatusMessage = "标注已删除";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除标注失败");
            await _notificationService.ShowErrorAsync("错误", "删除标注失败");
        }
    }

    /// <summary>
    /// 切换标注模式
    /// </summary>
    [RelayCommand]
    private void ToggleAnnotationMode()
    {
        IsAnnotationMode = !IsAnnotationMode;
        StatusMessage = IsAnnotationMode ? "标注模式已开启" : "标注模式已关闭";
    }

    /// <summary>
    /// 放大图像
    /// </summary>
    [RelayCommand]
    private void ZoomIn()
    {
        ZoomLevel = Math.Min(ZoomLevel * 1.2, 10.0);
        StatusMessage = $"缩放: {ZoomLevel:P0}";
    }

    /// <summary>
    /// 缩小图像
    /// </summary>
    [RelayCommand]
    private void ZoomOut()
    {
        ZoomLevel = Math.Max(ZoomLevel / 1.2, 0.1);
        StatusMessage = $"缩放: {ZoomLevel:P0}";
    }

    /// <summary>
    /// 重置缩放
    /// </summary>
    [RelayCommand]
    private void ResetZoom()
    {
        ZoomLevel = 1.0;
        ImageOffset = new Point(0, 0);
        StatusMessage = "缩放已重置";
    }

    /// <summary>
    /// 从路径加载图像
    /// </summary>
    private async Task LoadImageFromPathAsync(string filePath)
    {
        try
        {
            IsLoading = true;
            StatusMessage = "加载图像文件...";

            var fileInfo = new System.IO.FileInfo(filePath);
            var imageFileInfo = new Models.FileInfo
            {
                Name = fileInfo.Name,
                Path = fileInfo.FullName,
                Size = fileInfo.Length,
                CreatedTime = fileInfo.CreationTime,
                ModifiedTime = fileInfo.LastWriteTime,
                Extension = fileInfo.Extension,
                Type = "Image"
            };

            ImageFiles.Clear();
            ImageFiles.Add(imageFileInfo);
            SelectedImageFile = imageFileInfo;

            StatusMessage = $"已加载图像文件: {fileInfo.Name}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载图像文件失败: {FilePath}", filePath);
            await _notificationService.ShowErrorAsync("错误", "加载图像文件失败");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 加载选中的图像
    /// </summary>
    private async Task LoadImageAsync()
    {
        if (SelectedImageFile == null) return;

        try
        {
            IsLoading = true;
            StatusMessage = "解析图像文件...";

            // 根据文件类型加载图像
            if (SelectedImageFile.Extension.ToLower() == ".dcm")
            {
                // 处理DICOM文件
                try
                {
                    var metadata = await _imageProcessingService.ExtractDicomMetadataAsync(SelectedImageFile.Path);
                    var image = await _imageProcessingService.LoadDicomImageAsync(SelectedImageFile.Path);

                    if (image != null)
                    {
                        // 更新图像信息
                        var width = Convert.ToInt32(metadata.GetValueOrDefault("Columns", 0));
                        var height = Convert.ToInt32(metadata.GetValueOrDefault("Rows", 0));
                        var modality = metadata.GetValueOrDefault("Modality", "未知").ToString();

                        StatusMessage = $"DICOM文件加载成功: {SelectedImageFile.Name} ({width}x{height}, {modality})";
                    }
                    else
                    {
                        StatusMessage = "DICOM文件加载失败";
                        await _notificationService.ShowWarningAsync("警告", "DICOM文件加载失败，请检查文件格式");
                    }
                }
                catch (Exception dicomEx)
                {
                    _logger.LogError(dicomEx, "加载DICOM文件时发生错误");
                    StatusMessage = "DICOM文件处理失败";
                    await _notificationService.ShowErrorAsync("错误", "DICOM文件处理失败，请使用标准图像格式或检查文件完整性");
                }
            }
            else
            {
                // 处理标准图像格式
                StatusMessage = $"图像文件解析完成: {SelectedImageFile.Name}";
            }

            // 清除之前的标注
            Annotations.Clear();
            SelectedAnnotation = null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析图像文件失败");
            await _notificationService.ShowErrorAsync("错误", "解析图像文件失败");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 生成模拟标注数据
    /// </summary>
    private async Task GenerateMockAnnotations()
    {
        await Task.CompletedTask;

        Annotations.Clear();

        // 生成一些模拟标注
        var random = new Random();
        for (int i = 0; i < 3; i++)
        {
            var annotation = new AnnotationInfo
            {
                Id = Guid.NewGuid(),
                Label = AvailableClasses[random.Next(AvailableClasses.Count)],
                Confidence = 0.7 + random.NextDouble() * 0.3,
                BoundingBox = new Rect(
                    random.Next(50, 200),
                    random.Next(50, 200),
                    random.Next(50, 150),
                    random.Next(50, 150)
                ),
                Type = "Detection",
                CreatedTime = DateTime.Now
            };

            Annotations.Add(annotation);
        }
    }
}

/// <summary>
/// 标注信息模型
/// </summary>
public class AnnotationInfo
{
    public Guid Id { get; set; }
    public string Label { get; set; } = "";
    public double Confidence { get; set; }
    public Rect BoundingBox { get; set; }
    public string Type { get; set; } = "";
    public DateTime CreatedTime { get; set; }
    public string? Description { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}
