# 医学影像解析系统 (Medical Image Analysis System)

基于 .NET 8 的现代化医学影像解析系统，专为 DICOM 文件处理和 YOLOv11 模型训练优化。

## 🚀 主要特性

- **高精度 DICOM 处理**: 使用 fo-dicom 库实现精准的 DICOM 文件解析和元数据提取
- **智能影像处理**: 基于 ImageSharp 的高性能影像预处理和增强
- **YOLOv11 集成**: 完整的模型训练、验证和测试管道
- **智能标注系统**: 适配多种医学影像类型的自动标注功能
- **实时处理**: 基于 SignalR 的实时处理进度反馈
- **现代化架构**: 采用清洁架构和 DDD 设计模式
- **高性能**: 异步处理和内存优化
- **容器化部署**: Docker 支持

## 🏗️ 系统架构

```
src/
├── MedicalImageAnalysis.Core/          # 核心业务逻辑和领域模型
├── MedicalImageAnalysis.Application/   # 应用服务和用例
├── MedicalImageAnalysis.Infrastructure/ # 基础设施和外部依赖
├── MedicalImageAnalysis.Wpf/           # WPF 桌面应用 (主要)
└── MedicalImageAnalysis.WpfClient/     # WPF 客户端应用 (可运行)

data/
├── logs/                               # 应用程序日志
├── temp/                               # 临时文件
├── output/                             # 输出文件
└── models/                             # AI 模型文件

Brain/                                  # 示例 DICOM 文件
```

## 🛠️ 技术栈

- **.NET 8**: 最新 LTS 框架
- **WPF**: Windows 桌面应用程序框架
- **fo-dicom**: DICOM 文件处理
- **ImageSharp**: 高性能图像处理
- **ML.NET + ONNX**: 机器学习和 YOLOv11 集成 (开发中)
- **Entity Framework Core**: ORM
- **SQLite**: 本地数据存储
- **Serilog**: 结构化日志
- **Microsoft.Extensions.DependencyInjection**: 依赖注入
- **Microsoft.Extensions.Configuration**: 配置管理

## 📋 系统要求

### 运行环境
- **操作系统**: Windows 10/11 (x64)
- **.NET Runtime**: .NET 8.0 或更高版本
- **内存**: 至少 4GB RAM (推荐 8GB)
- **存储**: 至少 2GB 可用空间

### 开发环境
- **.NET 8 SDK**
- **Visual Studio 2022** 或 **VS Code**
- **Git** (版本控制)
- **支持 CUDA 的 GPU** (可选，用于 AI 训练)

## 🚀 快速开始

### 方式一：桌面应用程序 (推荐)

#### 1. 系统要求
- Windows 10/11 (x64)
- .NET 8 Runtime 或 SDK
- 至少 4GB RAM

#### 2. 启动桌面应用

**使用批处理文件 (推荐):**
```cmd
启动桌面端应用.bat
```

**使用 PowerShell:**
```powershell
.\启动桌面端应用.ps1
```

**手动启动:**
```bash
# 构建项目
dotnet build src/MedicalImageAnalysis.WpfClient/MedicalImageAnalysis.WpfClient.csproj

# 运行应用程序
dotnet run --project src/MedicalImageAnalysis.WpfClient
```

#### 3. 应用程序功能
- **DICOM 查看器**: 加载和查看医学影像
- **智能标注**: AI 辅助的标注工具
- **数据管理**: 文件组织和导出
- **系统监控**: 性能和状态监控

### 方式二：开发环境

#### 1. 安装依赖
```bash
dotnet restore
```

#### 2. 构建核心模块
```bash
dotnet build src/MedicalImageAnalysis.Core
```

#### 3. 运行客户端应用
```bash
dotnet run --project src/MedicalImageAnalysis.WpfClient
```

### 方式三：Docker 部署 (开发中)

```bash
# 注意：Docker 配置正在开发中
docker-compose up -d
```

## 📚 功能模块

### 桌面应用程序
- ✅ **现代化 WPF 界面**: 基于 .NET 8 的桌面应用
- ✅ **DICOM 查看器**: 支持医学影像显示和操作
- ✅ **智能标注工具**: 矩形、椭圆、多边形标注
- ✅ **数据管理**: 文件导入、组织和导出
- ✅ **系统监控**: 实时性能和状态监控
- ✅ **配置管理**: 灵活的应用程序设置

### DICOM 处理 (核心功能)
- ✅ **标准格式支持**: 支持所有标准 DICOM 格式
- ✅ **元数据提取**: 完整的 DICOM 标签解析和显示
- ✅ **图像加载显示**: 基于 fo-dicom 的高质量图像渲染
- ✅ **标签查看**: 完整的 DICOM 标签信息查看
- ✅ **影像方向检测**: 自动识别轴位/矢状位/冠状位
- 🚧 **Hounsfield 单位转换**: 精确的 CT 值计算 (开发中)
- 🚧 **多序列支持**: 复杂多序列研究处理 (开发中)

### 影像处理
- ✅ **基础显示**: 窗宽窗位调整、缩放、平移
- ✅ **DICOM 渲染**: 高质量的医学影像显示
- ✅ **格式转换**: DICOM → PNG/JPEG/BMP/TIFF 等
- ✅ **图像导出**: 支持多种格式的图像导出
- 🚧 **高级滤波**: 高斯、中值、双边滤波 (开发中)
- 🚧 **图像增强**: 直方图均衡化、对比度调整 (开发中)

### AI 模型集成
- 🚧 **YOLOv11 集成**: 目标检测和分割 (架构完成)
- 🚧 **模型训练**: 自定义数据集训练 (开发中)
- 🚧 **推理引擎**: 实时推理和批量处理 (开发中)
- 🚧 **模型管理**: 版本控制和性能评估 (开发中)

### 智能标注系统
- ✅ **手动标注**: 支持多种标注工具
- ✅ **DICOM 支持**: 完整支持 DICOM 文件标注
- ✅ **图像加载**: 支持 DICOM 和标准图像格式
- ✅ **AI 框架**: 基础 AI 推理框架完成
- 🚧 **自动标注**: AI 辅助标注生成 (开发中)
- 🚧 **格式支持**: YOLO、COCO、Pascal VOC 等 (开发中)
- 🚧 **质量验证**: 标注质量检查 (开发中)

### 数据管理
- ✅ **文件组织**: 项目和文件管理
- ✅ **导入导出**: 支持多种文件格式
- ✅ **日志系统**: 完整的操作日志记录
- ✅ **配置存储**: 用户设置和偏好保存

## 📖 使用指南

### 1. 启动应用程序

**方法一：使用批处理文件**
```cmd
# 双击运行或在命令行执行
启动桌面端应用.bat
```

**方法二：使用 PowerShell**
```powershell
# 在 PowerShell 中执行
.\启动桌面端应用.ps1
```

**方法三：手动构建和运行**
```bash
# 构建项目
dotnet build src/MedicalImageAnalysis.WpfClient

# 运行应用程序
dotnet run --project src/MedicalImageAnalysis.WpfClient
```

### 2. 加载 DICOM 文件

1. 启动应用程序后，点击 **"DICOM 查看器"** 选项卡
2. 使用 **"打开文件"** 按钮选择 DICOM 文件
3. 或者直接拖拽 DICOM 文件到应用程序窗口

### 3. 使用示例数据

项目包含示例 DICOM 文件：
```
Brain/
├── DJ01.dcm
├── DJ02.dcm
├── DJ03.dcm
└── ... (更多文件)
```

### 4. 标注功能

1. 在 DICOM 查看器中加载影像
2. 选择标注工具（矩形、椭圆、多边形）
3. 在影像上绘制标注
4. 保存标注结果

### 5. 查看日志

应用程序日志保存在：
```
logs/
└── wpf-client-YYYYMMDD.txt
```

## 🧪 测试和验证

### 构建测试
```bash
# 测试核心模块构建
dotnet build src/MedicalImageAnalysis.Core

# 测试客户端应用构建
dotnet build src/MedicalImageAnalysis.WpfClient

# 构建整个解决方案 (可能有错误)
dotnet build MedicalImageAnalysis.sln
```

### 功能测试
1. **启动测试**: 确认应用程序能正常启动
2. **DICOM 加载测试**: 使用 Brain/ 目录中的示例文件
3. **界面响应测试**: 测试各个功能模块的界面
4. **日志检查**: 查看 logs/ 目录中的日志文件

### 故障排除

**应用程序无法启动**
- 检查是否安装了 .NET 8 Runtime
- 查看日志文件中的错误信息
- 确认所有依赖项已正确安装

**DICOM 文件无法加载**
- 确认文件格式为标准 DICOM (.dcm)
- 检查文件是否损坏
- 查看应用程序日志中的错误信息

**性能问题**
- 确保系统有足够的可用内存
- 关闭不必要的后台程序
- 检查磁盘空间是否充足

## 🔧 配置说明

### 环境变量
- `ASPNETCORE_ENVIRONMENT`: 运行环境 (Development/Production)
- `ASPNETCORE_URLS`: 监听地址
- `Serilog__MinimumLevel__Default`: 日志级别

### 配置文件
主要配置在 `src/MedicalImageAnalysis.Api/appsettings.json` 中：

```json
{
  "MedicalImageAnalysis": {
    "MaxFileSize": 524288000,
    "SupportedFormats": ["dcm", "dicom"],
    "Processing": {
      "DefaultConfidenceThreshold": 0.5,
      "DefaultIouThreshold": 0.45
    }
  }
}
```

## 🏗️ 详细项目结构

```
medical-imaging/
├── src/                                    # 源代码目录
│   ├── MedicalImageAnalysis.Core/          # 核心业务逻辑层
│   │   ├── Entities/                       # 领域实体
│   │   ├── Interfaces/                     # 接口定义
│   │   ├── Models/                         # 数据模型
│   │   └── Data/                           # 数据访问
│   ├── MedicalImageAnalysis.Application/   # 应用服务层
│   │   └── Services/                       # 应用服务
│   ├── MedicalImageAnalysis.Infrastructure/ # 基础设施层
│   │   ├── Services/                       # 服务实现
│   │   ├── Algorithms/                     # 算法实现
│   │   └── Extensions/                     # 扩展方法
│   ├── MedicalImageAnalysis.Wpf/           # WPF 主应用 (开发中)
│   │   ├── Views/                          # 视图
│   │   ├── Services/                       # 服务
│   │   └── Windows/                        # 窗口
│   └── MedicalImageAnalysis.WpfClient/     # WPF 客户端 (可运行)
│       ├── Views/                          # 用户界面
│       ├── ViewModels/                     # 视图模型
│       ├── Services/                       # 客户端服务
│       └── Models/                         # 客户端模型
├── Brain/                                  # 示例 DICOM 文件
│   ├── DJ01.dcm ~ DJ10.dcm                 # 脑部 CT 示例
├── data/                                   # 数据目录
│   ├── logs/                               # 应用程序日志
│   ├── temp/                               # 临时文件
│   ├── output/                             # 处理结果
│   └── models/                             # AI 模型文件
├── logs/                                   # 运行时日志
├── scripts/                                # 辅助脚本
├── yolo_ohif/                              # YOLO + OHIF 集成
├── 启动桌面端应用.bat                        # Windows 启动脚本
├── 启动桌面端应用.ps1                        # PowerShell 启动脚本
├── MedicalImageAnalysis.sln                # Visual Studio 解决方案
├── docker-compose.yml                      # Docker 配置
└── README.md                               # 项目文档
```

## 🎯 核心特性

### 1. DICOM 处理引擎
- **完整的 DICOM 支持**: 基于 fo-dicom 库，支持所有标准 DICOM 格式
- **智能解析**: 自动检测影像方向 (轴位/矢状位/冠状位)
- **元数据提取**: 完整的 DICOM 标签解析和验证
- **Hounsfield 单位转换**: 精确的 CT 值计算
- **多序列支持**: 支持复杂的多序列研究

### 2. 高性能影像处理
- **多种滤波算法**: 高斯、中值、双边、非局部均值等
- **自适应增强**: 直方图均衡化、CLAHE、伽马校正
- **多平面重建**: MPR 功能，支持任意角度重建
- **格式转换**: 支持 PNG、JPEG、BMP、TIFF、WebP 等格式
- **实时预览**: 快速生成缩略图和预览

### 3. YOLOv11 AI 集成
- **模型训练**: 完整的训练流程，支持自定义数据集
- **实时推理**: 高性能推理引擎，支持批量处理
- **模型管理**: 模型版本控制、性能评估、格式转换
- **数据增强**: 多种增强策略，提升模型泛化能力

### 4. 智能标注系统
- **自动标注**: AI 驱动的智能标注生成
- **多格式支持**: YOLO、COCO、Pascal VOC、CVAT、LabelMe
- **质量验证**: 标注质量检查和异常检测
- **标注优化**: 边界框优化和重叠处理

### 5. 现代化 Web 界面
- **响应式设计**: 基于 Bootstrap 5 的现代化界面
- **实时反馈**: SignalR 实现的实时进度更新
- **拖拽上传**: 友好的文件上传体验
- **可视化图表**: 丰富的数据可视化组件

## 🔬 技术亮点

### 架构设计
- **清洁架构**: 分层设计，职责分离，易于维护和扩展
- **依赖注入**: 基于 .NET 8 的原生 DI 容器
- **异步编程**: 全面采用 async/await 模式，提升性能
- **错误处理**: 统一的异常处理和日志记录

### 性能优化
- **内存管理**: 优化的内存使用，支持大文件处理
- **并发处理**: 支持多线程并发处理
- **缓存策略**: 智能缓存机制，提升响应速度
- **流式处理**: 大文件流式处理，降低内存占用

### 安全性
- **输入验证**: 严格的输入验证和文件类型检查
- **错误隐藏**: 生产环境下隐藏敏感错误信息
- **资源限制**: 文件大小和处理时间限制
- **日志审计**: 完整的操作日志记录

## 🚀 部署指南

### 开发环境
1. 安装 .NET 8 SDK
2. 安装 Docker Desktop
3. 克隆项目并运行启动脚本

### 生产环境
1. 使用 Docker Compose 部署
2. 配置反向代理 (Nginx)
3. 设置监控和日志收集
4. 配置 HTTPS 证书

### 云部署
- **Azure**: 支持 Azure Container Instances
- **AWS**: 支持 ECS 和 EKS
- **Google Cloud**: 支持 Cloud Run 和 GKE
- **Kubernetes**: 提供完整的 K8s 配置文件

## 📦 部署

### Docker 部署
```bash
# 构建镜像
docker build -t medical-image-analysis .

# 运行容器
docker run -p 5000:80 medical-image-analysis
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如有问题或建议，请创建 [Issue](../../issues) 或联系开发团队。

## 🔄 版本历史

- **v1.0.0** - 当前版本 (开发中)
  - ✅ 基础 WPF 桌面应用程序
  - ✅ DICOM 文件加载和显示
  - ✅ 基础标注功能
  - ✅ 系统监控和日志
  - 🚧 YOLOv11 AI 模型集成 (开发中)
  - 🚧 高级图像处理功能 (开发中)
  - 🚧 Web API 服务 (架构完成，待修复)

## 📝 开发状态

### 已完成功能
- ✅ WPF 客户端应用程序框架
- ✅ DICOM 文件基础处理
- ✅ 用户界面和导航
- ✅ 日志系统和配置管理
- ✅ 项目结构和架构设计

### 开发中功能
- 🚧 完整的 DICOM 查看器
- 🚧 AI 模型训练和推理
- 🚧 高级图像处理算法
- 🚧 数据导入导出功能
- 🚧 Web API 服务修复

### 计划功能
- 📋 云端部署支持
- 📋 多用户协作功能
- 📋 移动端应用
- 📋 更多 AI 模型集成

---

**重要提示**:
- 本系统目前处于开发阶段，部分功能可能不完整
- 仅用于研究和教育目的，不应用于临床诊断
- 使用前请仔细阅读文档和测试功能
