using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Infrastructure.Algorithms;
using MedicalImageAnalysis.Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace MedicalImageAnalysis.Infrastructure.Services;

/// <summary>
/// 智能标注服务，提供高级的自动标注和标注优化功能
/// </summary>
public class SmartAnnotationService : ISmartAnnotationService
{
    private readonly ILogger<SmartAnnotationService> _logger;
    private readonly IAnnotationService _baseAnnotationService;
    private readonly IYoloService _yoloService;
    private readonly IImageProcessingService _imageProcessingService;
    private readonly IAdvancedImageProcessingService _advancedImageProcessingService;
    private readonly SmartAnnotationAlgorithms _algorithms;

    public SmartAnnotationService(
        ILogger<SmartAnnotationService> logger,
        IAnnotationService baseAnnotationService,
        IYoloService yoloService,
        IImageProcessingService imageProcessingService,
        IAdvancedImageProcessingService advancedImageProcessingService,
        SmartAnnotationAlgorithms algorithms)
    {
        _logger = logger;
        _baseAnnotationService = baseAnnotationService;
        _yoloService = yoloService;
        _imageProcessingService = imageProcessingService;
        _advancedImageProcessingService = advancedImageProcessingService;
        _algorithms = algorithms;
    }

    /// <summary>
    /// 智能标注生成
    /// </summary>
    public async Task<SmartAnnotationResult> GenerateSmartAnnotationsAsync(
        DicomInstance instance, 
        SmartAnnotationConfig config,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始智能标注生成，实例: {InstanceUid}", instance.SopInstanceUid);

        var result = new SmartAnnotationResult
        {
            InstanceId = instance.Id,
            StartTime = DateTime.UtcNow
        };

        try
        {
            // 1. 多模型集成推理
            var multiModelAnnotations = await MultiModelInferenceAsync(instance, config, cancellationToken);
            result.RawAnnotations = multiModelAnnotations;

            // 2. 标注融合
            var fusedAnnotations = await FuseAnnotationsAsync(multiModelAnnotations, config.FusionConfig);
            result.FusedAnnotations = fusedAnnotations;

            // 3. 后处理优化
            var optimizedAnnotations = await PostProcessAnnotationsAsync(fusedAnnotations, instance, config.PostProcessingConfig);
            result.OptimizedAnnotations = optimizedAnnotations;

            // 4. 质量评估
            var qualityScores = await EvaluateAnnotationQualityAsync(optimizedAnnotations, instance);
            result.QualityScores = qualityScores;

            // 5. 智能过滤
            var filteredAnnotations = await SmartFilterAnnotationsAsync(optimizedAnnotations, qualityScores, config.FilterConfig);
            result.FinalAnnotations = filteredAnnotations;

            result.Success = true;
            result.ProcessingTimeMs = (DateTime.UtcNow - result.StartTime).TotalMilliseconds;

            _logger.LogInformation("智能标注生成完成，生成 {Count} 个标注", filteredAnnotations.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "智能标注生成失败");
            result.Success = false;
            result.ErrorMessage = ex.Message;
            return result;
        }
    }

    /// <summary>
    /// 自适应标注优化
    /// </summary>
    public async Task<List<Annotation>> AdaptiveAnnotationOptimizationAsync(
        List<Annotation> annotations, 
        DicomInstance instance,
        AdaptiveOptimizationConfig config,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始自适应标注优化，标注数量: {Count}", annotations.Count);

        try
        {
            var optimizedAnnotations = new List<Annotation>();

            foreach (var annotation in annotations)
            {
                var optimized = await OptimizeSingleAnnotationAsync(annotation, instance, config, cancellationToken);
                optimizedAnnotations.Add(optimized);
            }

            // 全局优化
            optimizedAnnotations = await GlobalOptimizationAsync(optimizedAnnotations, config);

            _logger.LogInformation("自适应标注优化完成");
            return optimizedAnnotations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "自适应标注优化失败");
            throw;
        }
    }

    /// <summary>
    /// 标注质量自动评估
    /// </summary>
    public async Task<AnnotationQualityReport> AutoEvaluateAnnotationQualityAsync(
        List<Annotation> annotations, 
        DicomInstance instance,
        QualityEvaluationConfig config,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始标注质量自动评估");

        try
        {
            var report = new AnnotationQualityReport
            {
                InstanceId = instance.Id,
                EvaluationTime = DateTime.UtcNow,
                TotalAnnotations = annotations.Count
            };

            // 1. 几何质量评估
            var geometricScores = await EvaluateGeometricQualityAsync(annotations, instance);
            report.GeometricQualityScores = geometricScores;

            // 2. 语义质量评估
            var semanticScores = await EvaluateSemanticQualityAsync(annotations, instance, config);
            report.SemanticQualityScores = semanticScores;

            // 3. 一致性评估
            var consistencyScores = await EvaluateConsistencyAsync(annotations);
            report.ConsistencyScores = consistencyScores;

            // 4. 完整性评估
            var completenessScore = await EvaluateCompletenessAsync(annotations, instance, config);
            report.CompletenessScore = completenessScore;

            // 5. 计算总体质量分数
            report.OverallQualityScore = CalculateOverallQualityScore(report);

            // 6. 生成改进建议
            report.ImprovementSuggestions = await GenerateImprovementSuggestionsAsync(report, annotations);

            _logger.LogInformation("标注质量评估完成，总体质量分数: {Score:F2}", report.OverallQualityScore);
            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "标注质量评估失败");
            throw;
        }
    }

    /// <summary>
    /// 智能标注推荐
    /// </summary>
    public async Task<List<AnnotationRecommendation>> GenerateSmartRecommendationsAsync(
        DicomInstance instance, 
        List<Annotation> existingAnnotations,
        SmartRecommendationConfig config,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始生成智能标注推荐");

        try
        {
            var recommendations = new List<AnnotationRecommendation>();

            // 1. 基于上下文的推荐
            var contextRecommendations = await GenerateContextBasedRecommendationsAsync(instance, existingAnnotations, config);
            recommendations.AddRange(contextRecommendations);

            // 2. 基于模式识别的推荐
            var patternRecommendations = await GeneratePatternBasedRecommendationsAsync(instance, existingAnnotations, config);
            recommendations.AddRange(patternRecommendations);

            // 3. 基于解剖结构的推荐
            var anatomyRecommendations = await GenerateAnatomyBasedRecommendationsAsync(instance, existingAnnotations, config);
            recommendations.AddRange(anatomyRecommendations);

            // 4. 基于历史数据的推荐
            var historyRecommendations = await GenerateHistoryBasedRecommendationsAsync(instance, existingAnnotations, config);
            recommendations.AddRange(historyRecommendations);

            // 5. 推荐排序和过滤
            recommendations = await RankAndFilterRecommendationsAsync(recommendations, config);

            _logger.LogInformation("智能推荐生成完成，推荐数量: {Count}", recommendations.Count);
            return recommendations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "智能推荐生成失败");
            throw;
        }
    }

    /// <summary>
    /// 标注数据增强
    /// </summary>
    public async Task<AnnotationAugmentationResult> AugmentAnnotationDataAsync(
        List<Annotation> annotations, 
        DicomInstance instance,
        AnnotationAugmentationConfig config,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始标注数据增强");

        try
        {
            var result = new AnnotationAugmentationResult
            {
                OriginalAnnotations = annotations,
                AugmentationConfig = config
            };

            var augmentedData = new List<MedicalImageAnalysis.Core.Interfaces.AugmentedAnnotationData>();

            // 1. 几何变换增强
            if (config.EnableGeometricAugmentation)
            {
                var geometricAugmented = await ApplyGeometricAugmentationAsync(annotations, instance, config.GeometricConfig);
                augmentedData.AddRange(geometricAugmented.Select(x => new MedicalImageAnalysis.Core.Interfaces.AugmentedAnnotationData
                {
                    OriginalAnnotationId = x.OriginalAnnotation.Id,
                    AugmentedAnnotation = x.AugmentedAnnotation,
                    AugmentationType = x.AugmentationType,
                    AugmentationParameters = x.Parameters
                }));
            }

            // 2. 图像增强
            if (config.EnableImageAugmentation)
            {
                var imageAugmented = await ApplyImageAugmentationAsync(annotations, instance, config.ImageConfig);
                augmentedData.AddRange(imageAugmented.Select(x => new MedicalImageAnalysis.Core.Interfaces.AugmentedAnnotationData
                {
                    OriginalAnnotationId = x.OriginalAnnotation.Id,
                    AugmentedAnnotation = x.AugmentedAnnotation,
                    AugmentationType = x.AugmentationType,
                    AugmentationParameters = x.Parameters
                }));
            }

            // 3. 噪声增强
            if (config.EnableNoiseAugmentation)
            {
                var noiseAugmented = await ApplyNoiseAugmentationAsync(annotations, instance, config.NoiseConfig);
                augmentedData.AddRange(noiseAugmented.Select(x => new MedicalImageAnalysis.Core.Interfaces.AugmentedAnnotationData
                {
                    OriginalAnnotationId = x.OriginalAnnotation.Id,
                    AugmentedAnnotation = x.AugmentedAnnotation,
                    AugmentationType = x.AugmentationType,
                    AugmentationParameters = x.Parameters
                }));
            }

            // 4. 合成数据生成
            if (config.EnableSyntheticGeneration)
            {
                var syntheticData = await GenerateSyntheticAnnotationsAsync(annotations, instance, config.SyntheticConfig);
                augmentedData.AddRange(syntheticData.Select(x => new MedicalImageAnalysis.Core.Interfaces.AugmentedAnnotationData
                {
                    OriginalAnnotationId = x.OriginalAnnotation.Id,
                    AugmentedAnnotation = x.AugmentedAnnotation,
                    AugmentationType = x.AugmentationType,
                    AugmentationParameters = x.Parameters
                }));
            }

            result.AugmentedData = augmentedData;
            result.TotalAugmentedSamples = augmentedData.Count;
            result.Success = true;

            _logger.LogInformation("标注数据增强完成，生成 {Count} 个增强样本", augmentedData.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "标注数据增强失败");
            return new AnnotationAugmentationResult
            {
                Success = false,
                ErrorMessage = ex.Message,
                OriginalAnnotations = annotations,
                AugmentationConfig = config
            };
        }
    }

    #region 私有方法

    /// <summary>
    /// 优化单个标注
    /// </summary>
    private async Task<Annotation> OptimizeSingleAnnotationAsync(Annotation annotation, DicomInstance instance, AdaptiveOptimizationConfig config, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var optimized = new Annotation
        {
            Id = annotation.Id,
            Type = annotation.Type,
            Label = annotation.Label,
            Description = annotation.Description + " (已优化)",
            Confidence = annotation.Confidence,
            BoundingBox = annotation.BoundingBox,
            PolygonPoints = annotation.PolygonPoints,
            Source = annotation.Source,
            CreatedBy = annotation.CreatedBy,
            InstanceId = annotation.InstanceId,
            Instance = annotation.Instance,
            CreatedAt = annotation.CreatedAt,
            UpdatedAt = DateTime.UtcNow
        };

        // 边缘细化
        if (config.EnableEdgeRefinement)
        {
            optimized = await RefineAnnotationEdgesAsync(optimized, instance);
        }

        // 形状优化
        if (config.EnableShapeOptimization)
        {
            optimized = await OptimizeAnnotationShapeAsync(optimized, instance);
        }

        // 上下文调整
        if (config.EnableContextualAdjustment)
        {
            optimized = await AdjustAnnotationContextuallyAsync(optimized, instance);
        }

        return optimized;
    }

    /// <summary>
    /// 全局优化
    /// </summary>
    private async Task<List<Annotation>> GlobalOptimizationAsync(List<Annotation> annotations, AdaptiveOptimizationConfig config)
    {
        await Task.CompletedTask;

        // 去除重叠标注
        var nonOverlapping = RemoveOverlappingAnnotations(annotations);

        // 合并相邻标注
        var merged = MergeAdjacentAnnotations(nonOverlapping);

        return merged;
    }

    /// <summary>
    /// 应用几何增强
    /// </summary>
    private async Task<List<AugmentedAnnotationData>> ApplyGeometricAugmentationAsync(List<Annotation> annotations, DicomInstance instance, GeometricAugmentationConfig config)
    {
        await Task.CompletedTask;

        var augmentedData = new List<AugmentedAnnotationData>();

        foreach (var annotation in annotations)
        {
            // 旋转增强
            if (config.EnableRotation)
            {
                for (int i = 0; i < config.RotationSamples; i++)
                {
                    var angle = Random.Shared.NextDouble() * (config.RotationRange.Max - config.RotationRange.Min) + config.RotationRange.Min;
                    var rotatedAnnotation = RotateAnnotation(annotation, angle);
                    augmentedData.Add(new AugmentedAnnotationData
                    {
                        OriginalAnnotation = annotation,
                        AugmentedAnnotation = rotatedAnnotation,
                        AugmentationType = "Rotation",
                        Parameters = new Dictionary<string, object> { { "angle", angle } }
                    });
                }
            }

            // 缩放增强
            if (config.EnableScaling)
            {
                for (int i = 0; i < config.ScalingSamples; i++)
                {
                    var scale = Random.Shared.NextDouble() * (config.ScaleRange.Max - config.ScaleRange.Min) + config.ScaleRange.Min;
                    var scaledAnnotation = ScaleAnnotation(annotation, scale);
                    augmentedData.Add(new AugmentedAnnotationData
                    {
                        OriginalAnnotation = annotation,
                        AugmentedAnnotation = scaledAnnotation,
                        AugmentationType = "Scaling",
                        Parameters = new Dictionary<string, object> { { "scale", scale } }
                    });
                }
            }
        }

        return augmentedData;
    }

    /// <summary>
    /// 应用图像增强
    /// </summary>
    private async Task<List<AugmentedAnnotationData>> ApplyImageAugmentationAsync(List<Annotation> annotations, DicomInstance instance, ImageAugmentationConfig config)
    {
        await Task.CompletedTask;

        var augmentedData = new List<AugmentedAnnotationData>();

        foreach (var annotation in annotations)
        {
            // 亮度调整
            if (config.EnableBrightnessAdjustment)
            {
                var brightness = Random.Shared.NextDouble() * (config.BrightnessRange.Max - config.BrightnessRange.Min) + config.BrightnessRange.Min;
                augmentedData.Add(new AugmentedAnnotationData
                {
                    OriginalAnnotation = annotation,
                    AugmentedAnnotation = annotation, // 图像增强不改变标注本身
                    AugmentationType = "Brightness",
                    Parameters = new Dictionary<string, object> { { "brightness", brightness } }
                });
            }

            // 对比度调整
            if (config.EnableContrastAdjustment)
            {
                var contrast = Random.Shared.NextDouble() * (config.ContrastRange.Max - config.ContrastRange.Min) + config.ContrastRange.Min;
                augmentedData.Add(new AugmentedAnnotationData
                {
                    OriginalAnnotation = annotation,
                    AugmentedAnnotation = annotation,
                    AugmentationType = "Contrast",
                    Parameters = new Dictionary<string, object> { { "contrast", contrast } }
                });
            }
        }

        return augmentedData;
    }

    private async Task<List<List<Annotation>>> MultiModelInferenceAsync(DicomInstance instance, SmartAnnotationConfig config, CancellationToken cancellationToken)
    {
        var results = new List<List<Annotation>>();

        foreach (var modelConfig in config.ModelConfigs)
        {
            try
            {
                var annotations = await _baseAnnotationService.GenerateAnnotationsAsync(instance, modelConfig, cancellationToken);
                results.Add(annotations);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "模型 {ModelPath} 推理失败", modelConfig.ModelPath);
            }
        }

        return results;
    }

    private async Task<List<Annotation>> FuseAnnotationsAsync(List<List<Annotation>> multiModelAnnotations, AnnotationFusionConfig config)
    {
        await Task.CompletedTask;

        var fusedAnnotations = new List<Annotation>();

        // 简化的标注融合实现
        foreach (var modelAnnotations in multiModelAnnotations)
        {
            fusedAnnotations.AddRange(modelAnnotations);
        }

        // 去重和合并重叠标注
        fusedAnnotations = await RemoveDuplicateAnnotationsAsync(fusedAnnotations, config.OverlapThreshold);

        return fusedAnnotations;
    }

    private async Task<List<Annotation>> PostProcessAnnotationsAsync(List<Annotation> annotations, DicomInstance instance, PostProcessingConfig config)
    {
        await Task.CompletedTask;

        var processedAnnotations = new List<Annotation>();

        foreach (var annotation in annotations)
        {
            // 边界框优化
            if (config.OptimizeBoundingBoxes)
            {
                // 这里可以实现边界框优化逻辑
            }

            // 置信度校准
            if (config.CalibrateConfidence)
            {
                annotation.Confidence = CalibrateConfidence(annotation.Confidence, config.CalibrationParameters);
            }

            processedAnnotations.Add(annotation);
        }

        return processedAnnotations;
    }

    private async Task<List<AnnotationQualityScore>> EvaluateAnnotationQualityAsync(List<Annotation> annotations, DicomInstance instance)
    {
        await Task.CompletedTask;

        var qualityScores = new List<AnnotationQualityScore>();

        foreach (var annotation in annotations)
        {
            var score = new AnnotationQualityScore
            {
                AnnotationId = annotation.Id,
                GeometricScore = EvaluateGeometricQuality(annotation),
                SemanticScore = EvaluateSemanticQuality(annotation),
                ConfidenceScore = annotation.Confidence,
                OverallScore = 0.0
            };

            score.OverallScore = (score.GeometricScore + score.SemanticScore + score.ConfidenceScore) / 3.0;
            qualityScores.Add(score);
        }

        return qualityScores;
    }

    private async Task<List<Annotation>> SmartFilterAnnotationsAsync(List<Annotation> annotations, List<AnnotationQualityScore> qualityScores, SmartFilterConfig config)
    {
        await Task.CompletedTask;

        var filteredAnnotations = new List<Annotation>();

        for (int i = 0; i < annotations.Count && i < qualityScores.Count; i++)
        {
            var annotation = annotations[i];
            var qualityScore = qualityScores[i];

            if (qualityScore.OverallScore >= config.MinQualityThreshold &&
                annotation.Confidence >= config.MinConfidenceThreshold)
            {
                filteredAnnotations.Add(annotation);
            }
        }

        return filteredAnnotations;
    }

    private double EvaluateGeometricQuality(Annotation annotation)
    {
        // 简化的几何质量评估
        var area = annotation.BoundingBox.Width * annotation.BoundingBox.Height;
        var aspectRatio = annotation.BoundingBox.Width / annotation.BoundingBox.Height;

        // 基于面积和宽高比的质量评估
        var areaScore = Math.Min(area * 10, 1.0); // 面积越大质量越高（到一定程度）
        var aspectScore = Math.Min(aspectRatio, 1.0 / aspectRatio) * 2; // 宽高比越接近1质量越高

        return (areaScore + aspectScore) / 2.0;
    }

    private double EvaluateSemanticQuality(Annotation annotation)
    {
        // 简化的语义质量评估
        // 基于标签的完整性和描述的详细程度
        var labelScore = string.IsNullOrEmpty(annotation.Label) ? 0.0 : 0.8;
        var descriptionScore = string.IsNullOrEmpty(annotation.Description) ? 0.0 : 0.2;

        return labelScore + descriptionScore;
    }

    /// <summary>
    /// 细化标注边缘
    /// </summary>
    private async Task<Annotation> RefineAnnotationEdgesAsync(Annotation annotation, DicomInstance instance)
    {
        await Task.CompletedTask;
        // 简化实现，实际应该使用图像处理算法
        return annotation;
    }

    /// <summary>
    /// 优化标注形状
    /// </summary>
    private async Task<Annotation> OptimizeAnnotationShapeAsync(Annotation annotation, DicomInstance instance)
    {
        await Task.CompletedTask;
        // 简化实现，实际应该使用形状优化算法
        return annotation;
    }

    /// <summary>
    /// 上下文调整标注
    /// </summary>
    private async Task<Annotation> AdjustAnnotationContextuallyAsync(Annotation annotation, DicomInstance instance)
    {
        await Task.CompletedTask;
        // 简化实现，实际应该考虑周围组织结构
        return annotation;
    }

    /// <summary>
    /// 移除重叠标注
    /// </summary>
    private List<Annotation> RemoveOverlappingAnnotations(List<Annotation> annotations)
    {
        var result = new List<Annotation>();

        foreach (var annotation in annotations.OrderByDescending(a => a.Confidence))
        {
            bool hasOverlap = false;
            foreach (var existing in result)
            {
                if (CalculateIoU(annotation.BoundingBox, existing.BoundingBox) > 0.5)
                {
                    hasOverlap = true;
                    break;
                }
            }

            if (!hasOverlap)
            {
                result.Add(annotation);
            }
        }

        return result;
    }

    /// <summary>
    /// 合并相邻标注
    /// </summary>
    private List<Annotation> MergeAdjacentAnnotations(List<Annotation> annotations)
    {
        // 简化实现，实际应该实现更复杂的合并逻辑
        return annotations;
    }

    /// <summary>
    /// 旋转标注
    /// </summary>
    private Annotation RotateAnnotation(Annotation annotation, double angle)
    {
        // 简化实现，实际应该实现坐标变换
        var rotated = new Annotation
        {
            Id = Guid.NewGuid(),
            Type = annotation.Type,
            Label = annotation.Label,
            Description = annotation.Description,
            Confidence = annotation.Confidence,
            BoundingBox = annotation.BoundingBox, // 简化：不改变边界框
            Source = annotation.Source,
            CreatedBy = annotation.CreatedBy,
            InstanceId = annotation.InstanceId
        };

        return rotated;
    }

    /// <summary>
    /// 缩放标注
    /// </summary>
    private Annotation ScaleAnnotation(Annotation annotation, double scale)
    {
        var scaled = new Annotation
        {
            Id = Guid.NewGuid(),
            Type = annotation.Type,
            Label = annotation.Label,
            Description = annotation.Description,
            Confidence = annotation.Confidence,
            BoundingBox = new BoundingBox
            {
                CenterX = annotation.BoundingBox.CenterX,
                CenterY = annotation.BoundingBox.CenterY,
                Width = annotation.BoundingBox.Width * scale,
                Height = annotation.BoundingBox.Height * scale
            },
            Source = annotation.Source,
            CreatedBy = annotation.CreatedBy,
            InstanceId = annotation.InstanceId
        };

        return scaled;
    }

    /// <summary>
    /// 计算IoU
    /// </summary>
    private double CalculateIoU(BoundingBox box1, BoundingBox box2)
    {
        var x1 = Math.Max(box1.Left, box2.Left);
        var y1 = Math.Max(box1.Top, box2.Top);
        var x2 = Math.Min(box1.Right, box2.Right);
        var y2 = Math.Min(box1.Bottom, box2.Bottom);

        if (x2 <= x1 || y2 <= y1) return 0.0;

        var intersection = (x2 - x1) * (y2 - y1);
        var area1 = box1.Width * box1.Height;
        var area2 = box2.Width * box2.Height;
        var union = area1 + area2 - intersection;

        return intersection / union;
    }

    private double CalibrateConfidence(double originalConfidence, Dictionary<string, object> calibrationParameters)
    {
        // 简化的置信度校准
        var alpha = calibrationParameters.GetValueOrDefault("alpha", 1.0);
        var beta = calibrationParameters.GetValueOrDefault("beta", 0.0);

        return Math.Max(0.0, Math.Min(1.0, (double)alpha * originalConfidence + (double)beta));
    }

    private async Task<List<Annotation>> RemoveDuplicateAnnotationsAsync(List<Annotation> annotations, double overlapThreshold)
    {
        await Task.CompletedTask;

        var uniqueAnnotations = new List<Annotation>();

        foreach (var annotation in annotations)
        {
            bool isDuplicate = false;

            foreach (var existing in uniqueAnnotations)
            {
                var overlap = CalculateOverlap(annotation.BoundingBox, existing.BoundingBox);
                if (overlap > overlapThreshold)
                {
                    isDuplicate = true;
                    // 保留置信度更高的标注
                    if (annotation.Confidence > existing.Confidence)
                    {
                        uniqueAnnotations.Remove(existing);
                        uniqueAnnotations.Add(annotation);
                    }
                    break;
                }
            }

            if (!isDuplicate)
            {
                uniqueAnnotations.Add(annotation);
            }
        }

        return uniqueAnnotations;
    }

    private double CalculateOverlap(BoundingBox box1, BoundingBox box2)
    {
        var x1 = Math.Max(box1.CenterX - box1.Width / 2, box2.CenterX - box2.Width / 2);
        var y1 = Math.Max(box1.CenterY - box1.Height / 2, box2.CenterY - box2.Height / 2);
        var x2 = Math.Min(box1.CenterX + box1.Width / 2, box2.CenterX + box2.Width / 2);
        var y2 = Math.Min(box1.CenterY + box1.Height / 2, box2.CenterY + box2.Height / 2);

        if (x2 <= x1 || y2 <= y1) return 0.0;

        var intersectionArea = (x2 - x1) * (y2 - y1);
        var unionArea = box1.Width * box1.Height + box2.Width * box2.Height - intersectionArea;

        return intersectionArea / unionArea;
    }

    // 添加缺失的方法实现
    private async Task<List<AnnotationQualityScore>> EvaluateGeometricQualityAsync(List<Annotation> annotations, DicomInstance instance)
    {
        await Task.CompletedTask;

        var qualityScores = new List<AnnotationQualityScore>();

        foreach (var annotation in annotations)
        {
            var geometricScore = await EvaluateAdvancedGeometricQualityAsync(annotation, instance);

            qualityScores.Add(new AnnotationQualityScore
            {
                AnnotationId = annotation.Id,
                GeometricScore = geometricScore,
                SemanticScore = 0.8,
                ConfidenceScore = annotation.Confidence,
                OverallScore = geometricScore
            });
        }

        return qualityScores;
    }

    private async Task<double> EvaluateAdvancedGeometricQualityAsync(Annotation annotation, DicomInstance instance)
    {
        await Task.CompletedTask;

        var scores = new List<double>();

        // 1. 边界框合理性评估
        var boundingBoxScore = EvaluateBoundingBoxReasonableness(annotation, instance);
        scores.Add(boundingBoxScore);

        // 2. 尺寸合理性评估
        var sizeScore = EvaluateSizeReasonableness(annotation, instance);
        scores.Add(sizeScore);

        // 3. 位置合理性评估
        var positionScore = EvaluatePositionReasonableness(annotation, instance);
        scores.Add(positionScore);

        // 4. 形状合理性评估
        var shapeScore = EvaluateShapeReasonableness(annotation);
        scores.Add(shapeScore);

        // 5. 边界清晰度评估
        var boundaryScore = await EvaluateBoundaryClarityAsync(annotation, instance);
        scores.Add(boundaryScore);

        return scores.Average();
    }

    private double EvaluateBoundingBoxReasonableness(Annotation annotation, DicomInstance instance)
    {
        var bbox = annotation.BoundingBox;

        // 检查边界框是否在图像范围内
        if (bbox.CenterX < 0 || bbox.CenterX > 1 || bbox.CenterY < 0 || bbox.CenterY > 1)
            return 0.0;

        // 检查边界框尺寸是否合理
        if (bbox.Width <= 0 || bbox.Height <= 0 || bbox.Width > 1 || bbox.Height > 1)
            return 0.2;

        // 检查宽高比是否合理
        var aspectRatio = bbox.Width / bbox.Height;
        var aspectScore = aspectRatio > 0.1 && aspectRatio < 10 ? 1.0 : 0.5;

        // 检查面积是否合理
        var area = bbox.Width * bbox.Height;
        var areaScore = area > 0.001 && area < 0.8 ? 1.0 : 0.7;

        return (aspectScore + areaScore) / 2.0;
    }

    private double EvaluateSizeReasonableness(Annotation annotation, DicomInstance instance)
    {
        var bbox = annotation.BoundingBox;
        var area = bbox.Width * bbox.Height;

        // 基于标注类型评估尺寸合理性
        var expectedSizeRange = GetExpectedSizeRange(annotation.Label);

        if (area >= expectedSizeRange.min && area <= expectedSizeRange.max)
            return 1.0;
        else if (area >= expectedSizeRange.min * 0.5 && area <= expectedSizeRange.max * 2.0)
            return 0.7;
        else
            return 0.3;
    }

    private double EvaluatePositionReasonableness(Annotation annotation, DicomInstance instance)
    {
        var bbox = annotation.BoundingBox;
        var label = annotation.Label.ToLowerInvariant();

        // 基于解剖知识评估位置合理性
        var expectedPosition = GetExpectedAnatomyPosition(label);

        var distance = Math.Sqrt(
            Math.Pow(bbox.CenterX - expectedPosition.x, 2) +
            Math.Pow(bbox.CenterY - expectedPosition.y, 2));

        return Math.Max(0.0, 1.0 - distance * 2.0);
    }

    private double EvaluateShapeReasonableness(Annotation annotation)
    {
        var bbox = annotation.BoundingBox;
        var aspectRatio = bbox.Width / bbox.Height;

        // 基于标注类型评估形状合理性
        var expectedAspectRatio = GetExpectedAspectRatio(annotation.Label);

        var ratioDeviation = Math.Abs(aspectRatio - expectedAspectRatio) / expectedAspectRatio;
        return Math.Max(0.0, 1.0 - ratioDeviation);
    }

    private async Task<double> EvaluateBoundaryClarityAsync(Annotation annotation, DicomInstance instance)
    {
        await Task.CompletedTask;

        // 模拟边界清晰度评估
        // 在实际实现中，这里会分析边界区域的梯度强度

        var random = new Random();
        return 0.5 + random.NextDouble() * 0.5; // 模拟0.5-1.0的分数
    }

    private (double min, double max) GetExpectedSizeRange(string label)
    {
        return label.ToLowerInvariant() switch
        {
            var l when l.Contains("心脏") => (0.05, 0.25),
            var l when l.Contains("肝脏") => (0.1, 0.4),
            var l when l.Contains("肾脏") => (0.02, 0.1),
            var l when l.Contains("肺野") => (0.2, 0.6),
            var l when l.Contains("脑室") => (0.01, 0.1),
            var l when l.Contains("肿瘤") => (0.001, 0.2),
            _ => (0.001, 0.5)
        };
    }

    private (double x, double y) GetExpectedAnatomyPosition(string label)
    {
        return label switch
        {
            var l when l.Contains("心脏") => (0.4, 0.6),
            var l when l.Contains("肝脏") => (0.6, 0.5),
            var l when l.Contains("脑室") => (0.5, 0.4),
            var l when l.Contains("肺野") => (0.5, 0.5),
            _ => (0.5, 0.5)
        };
    }

    private double GetExpectedAspectRatio(string label)
    {
        return label.ToLowerInvariant() switch
        {
            var l when l.Contains("心脏") => 1.2,
            var l when l.Contains("肝脏") => 1.5,
            var l when l.Contains("肾脏") => 0.8,
            var l when l.Contains("脑室") => 1.0,
            _ => 1.0
        };
    }

    private async Task<List<AnnotationQualityScore>> EvaluateSemanticQualityAsync(List<Annotation> annotations, DicomInstance instance, QualityEvaluationConfig config)
    {
        await Task.CompletedTask;
        return annotations.Select(a => new AnnotationQualityScore
        {
            AnnotationId = a.Id,
            GeometricScore = 0.8,
            SemanticScore = EvaluateSemanticQuality(a),
            ConfidenceScore = a.Confidence,
            OverallScore = EvaluateSemanticQuality(a)
        }).ToList();
    }

    private async Task<List<AnnotationQualityScore>> EvaluateConsistencyAsync(List<Annotation> annotations)
    {
        await Task.CompletedTask;
        return annotations.Select(a => new AnnotationQualityScore
        {
            AnnotationId = a.Id,
            GeometricScore = 0.8,
            SemanticScore = 0.8,
            ConfidenceScore = a.Confidence,
            OverallScore = 0.8
        }).ToList();
    }

    private async Task<double> EvaluateCompletenessAsync(List<Annotation> annotations, DicomInstance instance, QualityEvaluationConfig config)
    {
        await Task.CompletedTask;
        return annotations.Count > 0 ? 0.8 : 0.0;
    }

    private double CalculateOverallQualityScore(AnnotationQualityReport report)
    {
        var scores = new List<double>();

        if (report.GeometricQualityScores.Any())
            scores.Add(report.GeometricQualityScores.Average(s => s.OverallScore));

        if (report.SemanticQualityScores.Any())
            scores.Add(report.SemanticQualityScores.Average(s => s.OverallScore));

        if (report.ConsistencyScores.Any())
            scores.Add(report.ConsistencyScores.Average(s => s.OverallScore));

        scores.Add(report.CompletenessScore);

        return scores.Any() ? scores.Average() : 0.0;
    }

    private async Task<List<string>> GenerateImprovementSuggestionsAsync(AnnotationQualityReport report, List<Annotation> annotations)
    {
        await Task.CompletedTask;
        var suggestions = new List<string>();

        if (report.OverallQualityScore < 0.7)
        {
            suggestions.Add("建议提高标注精度");
        }

        if (report.CompletenessScore < 0.8)
        {
            suggestions.Add("建议增加标注覆盖范围");
        }

        return suggestions;
    }

    private async Task<List<AnnotationRecommendation>> GenerateContextBasedRecommendationsAsync(DicomInstance instance, List<Annotation> existingAnnotations, SmartRecommendationConfig config)
    {
        await Task.CompletedTask;

        var recommendations = new List<AnnotationRecommendation>();

        try
        {
            // 1. 基于解剖位置的推荐
            var anatomyRecommendations = await GenerateAnatomyBasedRecommendationsAsync(instance, existingAnnotations);
            recommendations.AddRange(anatomyRecommendations);

            // 2. 基于影像特征的推荐
            var featureRecommendations = await GenerateFeatureBasedRecommendationsAsync(instance, existingAnnotations);
            recommendations.AddRange(featureRecommendations);

            // 3. 基于临床上下文的推荐
            var clinicalRecommendations = await GenerateClinicalContextRecommendationsAsync(instance, existingAnnotations);
            recommendations.AddRange(clinicalRecommendations);

            // 4. 基于相似病例的推荐
            var similarCaseRecommendations = await GenerateSimilarCaseRecommendationsAsync(instance, existingAnnotations);
            recommendations.AddRange(similarCaseRecommendations);

            _logger.LogInformation("生成基于上下文的推荐 {Count} 个", recommendations.Count);
            return recommendations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成基于上下文的推荐失败");
            return recommendations;
        }
    }

    private async Task<List<AnnotationRecommendation>> GenerateAnatomyBasedRecommendationsAsync(DicomInstance instance, List<Annotation> existingAnnotations)
    {
        await Task.CompletedTask;

        var recommendations = new List<AnnotationRecommendation>();

        // 根据检查部位推荐常见的解剖结构标注
        var bodyPart = instance.Series?.Study?.BodyPart?.ToUpperInvariant() ?? "";
        var modality = instance.Series?.Modality?.ToUpperInvariant() ?? "";

        var anatomyRecommendations = (bodyPart, modality) switch
        {
            (var part, "CT") when part.Contains("BRAIN") || part.Contains("HEAD") => new[]
            {
                ("脑室", 0.8, "大脑解剖结构"),
                ("脑干", 0.7, "重要神经结构"),
                ("小脑", 0.7, "运动协调中枢"),
                ("颅骨", 0.6, "保护性骨结构")
            },
            (var part, "CT") when part.Contains("CHEST") || part.Contains("LUNG") => new[]
            {
                ("肺野", 0.9, "主要呼吸器官"),
                ("心脏", 0.8, "循环系统中心"),
                ("主动脉", 0.7, "主要血管"),
                ("肋骨", 0.6, "胸廓骨结构")
            },
            (var part, "CT") when part.Contains("ABDOMEN") => new[]
            {
                ("肝脏", 0.9, "最大内脏器官"),
                ("肾脏", 0.8, "泌尿系统器官"),
                ("脾脏", 0.7, "免疫系统器官"),
                ("胰腺", 0.6, "内分泌器官")
            },
            _ => Array.Empty<(string, double, string)>()
        };

        foreach (var (label, confidence, reason) in anatomyRecommendations)
        {
            // 检查是否已经存在相似标注
            if (!existingAnnotations.Any(a => a.Label.Contains(label, StringComparison.OrdinalIgnoreCase)))
            {
                recommendations.Add(new AnnotationRecommendation
                {
                    RecommendedLabel = label,
                    Confidence = confidence,
                    Reason = $"基于{bodyPart}解剖结构: {reason}",
                    Priority = confidence > 0.8 ? RecommendationPriority.High : RecommendationPriority.Medium,
                    RecommendedBoundingBox = EstimateAnatomyBoundingBox(label, instance)
                });
            }
        }

        return recommendations;
    }

    private async Task<List<AnnotationRecommendation>> GenerateFeatureBasedRecommendationsAsync(DicomInstance instance, List<Annotation> existingAnnotations)
    {
        await Task.CompletedTask;

        var recommendations = new List<AnnotationRecommendation>();

        try
        {
            // 基于图像特征分析推荐标注
            // 这里可以集成图像处理算法来检测特定特征

            // 1. 检测高密度区域（可能的钙化、骨骼等）
            var highDensityRecommendations = await DetectHighDensityRegionsAsync(instance);
            recommendations.AddRange(highDensityRecommendations);

            // 2. 检测低密度区域（可能的囊肿、气体等）
            var lowDensityRecommendations = await DetectLowDensityRegionsAsync(instance);
            recommendations.AddRange(lowDensityRecommendations);

            // 3. 检测边缘特征（可能的器官边界）
            var edgeRecommendations = await DetectEdgeFeaturesAsync(instance);
            recommendations.AddRange(edgeRecommendations);

            return recommendations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "基于特征的推荐生成失败");
            return recommendations;
        }
    }

    private async Task<List<AnnotationRecommendation>> GenerateClinicalContextRecommendationsAsync(DicomInstance instance, List<Annotation> existingAnnotations)
    {
        await Task.CompletedTask;

        var recommendations = new List<AnnotationRecommendation>();

        // 基于临床信息推荐相关标注
        var studyDescription = instance.Series?.Study?.StudyDescription ?? "";
        var patientAgeStr = instance.Series?.Study?.Patient?.PatientAge ?? "0";
        var patientSex = instance.Series?.Study?.Patient?.PatientSex ?? "";

        // 尝试解析年龄
        int.TryParse(patientAgeStr.Replace("Y", "").Replace("y", ""), out int patientAge);

        // 根据检查描述推荐相关标注
        if (studyDescription.Contains("肿瘤", StringComparison.OrdinalIgnoreCase) ||
            studyDescription.Contains("tumor", StringComparison.OrdinalIgnoreCase))
        {
            recommendations.Add(new AnnotationRecommendation
            {
                RecommendedLabel = "疑似肿瘤区域",
                Confidence = 0.7,
                Reason = "基于检查描述中的肿瘤关键词",
                Priority = RecommendationPriority.High
            });
        }

        // 根据患者年龄推荐相关标注
        if (patientAge > 60)
        {
            recommendations.Add(new AnnotationRecommendation
            {
                RecommendedLabel = "退行性改变",
                Confidence = 0.6,
                Reason = "基于患者年龄，常见退行性改变",
                Priority = RecommendationPriority.Medium
            });
        }

        return recommendations;
    }

    private async Task<List<AnnotationRecommendation>> GenerateSimilarCaseRecommendationsAsync(DicomInstance instance, List<Annotation> existingAnnotations)
    {
        await Task.CompletedTask;

        var recommendations = new List<AnnotationRecommendation>();

        // 这里可以实现基于相似病例的推荐逻辑
        // 例如：查找相似的影像特征、患者特征等

        // 模拟基于相似病例的推荐
        if (existingAnnotations.Any())
        {
            var mostCommonLabel = existingAnnotations
                .GroupBy(a => a.Label)
                .OrderByDescending(g => g.Count())
                .FirstOrDefault()?.Key;

            if (!string.IsNullOrEmpty(mostCommonLabel))
            {
                recommendations.Add(new AnnotationRecommendation
                {
                    RecommendedLabel = $"相关_{mostCommonLabel}",
                    Confidence = 0.5,
                    Reason = "基于相似病例中的常见标注模式",
                    Priority = RecommendationPriority.Low
                });
            }
        }

        return recommendations;
    }

    private BoundingBox EstimateAnatomyBoundingBox(string anatomyLabel, DicomInstance instance)
    {
        // 基于解剖知识估算边界框位置
        var width = instance.Columns;
        var height = instance.Rows;

        return anatomyLabel.ToLowerInvariant() switch
        {
            var label when label.Contains("脑室") => new BoundingBox
            {
                CenterX = 0.5, CenterY = 0.4, Width = 0.3, Height = 0.2
            },
            var label when label.Contains("心脏") => new BoundingBox
            {
                CenterX = 0.4, CenterY = 0.6, Width = 0.25, Height = 0.3
            },
            var label when label.Contains("肝脏") => new BoundingBox
            {
                CenterX = 0.6, CenterY = 0.5, Width = 0.4, Height = 0.35
            },
            var label when label.Contains("肺野") => new BoundingBox
            {
                CenterX = 0.5, CenterY = 0.5, Width = 0.7, Height = 0.6
            },
            _ => new BoundingBox
            {
                CenterX = 0.5, CenterY = 0.5, Width = 0.2, Height = 0.2
            }
        };
    }

    private async Task<List<AnnotationRecommendation>> DetectHighDensityRegionsAsync(DicomInstance instance)
    {
        await Task.CompletedTask;

        var recommendations = new List<AnnotationRecommendation>();

        // 模拟高密度区域检测
        // 在实际实现中，这里会使用图像处理算法分析像素值分布

        recommendations.Add(new AnnotationRecommendation
        {
            RecommendedLabel = "高密度区域",
            Confidence = 0.6,
            Reason = "检测到异常高密度信号",
            Priority = RecommendationPriority.Medium,
            RecommendedBoundingBox = new BoundingBox
            {
                CenterX = 0.3, CenterY = 0.3, Width = 0.1, Height = 0.1
            }
        });

        return recommendations;
    }

    private async Task<List<AnnotationRecommendation>> DetectLowDensityRegionsAsync(DicomInstance instance)
    {
        await Task.CompletedTask;

        var recommendations = new List<AnnotationRecommendation>();

        // 模拟低密度区域检测
        recommendations.Add(new AnnotationRecommendation
        {
            RecommendedLabel = "低密度区域",
            Confidence = 0.5,
            Reason = "检测到异常低密度信号",
            Priority = RecommendationPriority.Low,
            RecommendedBoundingBox = new BoundingBox
            {
                CenterX = 0.7, CenterY = 0.7, Width = 0.15, Height = 0.15
            }
        });

        return recommendations;
    }

    private async Task<List<AnnotationRecommendation>> DetectEdgeFeaturesAsync(DicomInstance instance)
    {
        await Task.CompletedTask;

        var recommendations = new List<AnnotationRecommendation>();

        // 模拟边缘特征检测
        recommendations.Add(new AnnotationRecommendation
        {
            RecommendedLabel = "器官边界",
            Confidence = 0.4,
            Reason = "检测到明显的边缘特征",
            Priority = RecommendationPriority.Low,
            RecommendedBoundingBox = new BoundingBox
            {
                CenterX = 0.5, CenterY = 0.6, Width = 0.3, Height = 0.25
            }
        });

        return recommendations;
    }

    private async Task<List<AnnotationRecommendation>> GeneratePatternBasedRecommendationsAsync(DicomInstance instance, List<Annotation> existingAnnotations, SmartRecommendationConfig config)
    {
        await Task.CompletedTask;
        return new List<AnnotationRecommendation>();
    }

    private async Task<List<AnnotationRecommendation>> GenerateAnatomyBasedRecommendationsAsync(DicomInstance instance, List<Annotation> existingAnnotations, SmartRecommendationConfig config)
    {
        await Task.CompletedTask;
        return new List<AnnotationRecommendation>();
    }

    private async Task<List<AnnotationRecommendation>> GenerateHistoryBasedRecommendationsAsync(DicomInstance instance, List<Annotation> existingAnnotations, SmartRecommendationConfig config)
    {
        await Task.CompletedTask;
        return new List<AnnotationRecommendation>();
    }

    private async Task<List<AnnotationRecommendation>> RankAndFilterRecommendationsAsync(List<AnnotationRecommendation> recommendations, SmartRecommendationConfig config)
    {
        await Task.CompletedTask;
        return recommendations.OrderByDescending(r => r.Confidence).Take(config.MaxRecommendations).ToList();
    }

    private async Task<List<AugmentedAnnotationData>> ApplyGeometricTransformationsAsync(List<Annotation> annotations, DicomInstance instance, object config)
    {
        await Task.CompletedTask;
        return new List<AugmentedAnnotationData>();
    }

    private async Task<List<AugmentedAnnotationData>> ApplyColorAugmentationAsync(List<Annotation> annotations, DicomInstance instance, object config)
    {
        await Task.CompletedTask;
        return new List<AugmentedAnnotationData>();
    }

    private async Task<List<AugmentedAnnotationData>> ApplyNoiseAugmentationAsync(List<Annotation> annotations, DicomInstance instance, NoiseAugmentationConfig config)
    {
        await Task.CompletedTask;

        var augmentedData = new List<AugmentedAnnotationData>();

        foreach (var annotation in annotations)
        {
            // 高斯噪声
            if (config.EnableGaussianNoise)
            {
                for (int i = 0; i < config.GaussianNoiseSamples; i++)
                {
                    augmentedData.Add(new AugmentedAnnotationData
                    {
                        OriginalAnnotation = annotation,
                        AugmentedAnnotation = annotation, // 噪声增强不改变标注本身
                        AugmentationType = "GaussianNoise",
                        Parameters = new Dictionary<string, object> { { "std", config.GaussianNoiseStd } }
                    });
                }
            }
        }

        return augmentedData;
    }

    private async Task<List<AugmentedAnnotationData>> GenerateSyntheticAnnotationsAsync(List<Annotation> annotations, DicomInstance instance, SyntheticGenerationConfig config)
    {
        await Task.CompletedTask;

        var augmentedData = new List<AugmentedAnnotationData>();

        foreach (var annotation in annotations)
        {
            // 模板匹配生成
            if (config.EnableTemplateMatching)
            {
                for (int i = 0; i < config.SyntheticSamplesPerOriginal; i++)
                {
                    var syntheticAnnotation = new Annotation
                    {
                        Id = Guid.NewGuid(),
                        Type = annotation.Type,
                        Label = annotation.Label,
                        Description = annotation.Description + " (合成)",
                        Confidence = annotation.Confidence * 0.9, // 略微降低置信度
                        BoundingBox = annotation.BoundingBox,
                        Source = AnnotationSource.AI,
                        CreatedBy = "Synthetic Generator",
                        InstanceId = annotation.InstanceId
                    };

                    augmentedData.Add(new AugmentedAnnotationData
                    {
                        OriginalAnnotation = annotation,
                        AugmentedAnnotation = syntheticAnnotation,
                        AugmentationType = "Synthetic",
                        Parameters = new Dictionary<string, object> { { "method", "template_matching" } }
                    });
                }
            }
        }

        return augmentedData;
    }

    #endregion
}

/// <summary>
/// 增强标注数据
/// </summary>
public class AugmentedAnnotationData
{
    public Annotation OriginalAnnotation { get; set; } = null!;
    public Annotation AugmentedAnnotation { get; set; } = null!;
    public string AugmentationType { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
