using System;

namespace MedicalImageAnalysis.WpfClient.Models;

/// <summary>
/// 导航项模型
/// </summary>
public class NavigationItem
{
    /// <summary>
    /// 导航项ID
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 图标
    /// </summary>
    public string Icon { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 视图类型
    /// </summary>
    public Type? ViewType { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 是否可见
    /// </summary>
    public bool IsVisible { get; set; } = true;

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int Order { get; set; }

    /// <summary>
    /// 分组
    /// </summary>
    public string Group { get; set; } = string.Empty;

    /// <summary>
    /// 快捷键
    /// </summary>
    public string Shortcut { get; set; } = string.Empty;

    /// <summary>
    /// 工具提示
    /// </summary>
    public string ToolTip { get; set; } = string.Empty;

    /// <summary>
    /// 是否为分隔符
    /// </summary>
    public bool IsSeparator { get; set; } = false;

    /// <summary>
    /// 徽章文本
    /// </summary>
    public string Badge { get; set; } = string.Empty;

    /// <summary>
    /// 徽章颜色
    /// </summary>
    public string BadgeColor { get; set; } = "#FF0000";

    /// <summary>
    /// 是否显示徽章
    /// </summary>
    public bool ShowBadge { get; set; } = false;
}

/// <summary>
/// 连接状态枚举
/// </summary>
public enum ConnectionStatus
{
    /// <summary>
    /// 未连接
    /// </summary>
    Disconnected = 0,

    /// <summary>
    /// 连接中
    /// </summary>
    Connecting = 1,

    /// <summary>
    /// 已连接
    /// </summary>
    Connected = 2,

    /// <summary>
    /// 连接失败
    /// </summary>
    Failed = 3,

    /// <summary>
    /// 重连中
    /// </summary>
    Reconnecting = 4,

    /// <summary>
    /// 错误状态
    /// </summary>
    Error = 5
}

/// <summary>
/// 应用程序状态
/// </summary>
public class ApplicationStatus
{
    /// <summary>
    /// 应用程序版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 构建时间
    /// </summary>
    public DateTime BuildTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 运行时间
    /// </summary>
    public TimeSpan Uptime { get; set; }

    /// <summary>
    /// 内存使用量（MB）
    /// </summary>
    public double MemoryUsage { get; set; }

    /// <summary>
    /// CPU使用率（%）
    /// </summary>
    public double CpuUsage { get; set; }

    /// <summary>
    /// 连接状态
    /// </summary>
    public ConnectionStatus ConnectionStatus { get; set; } = ConnectionStatus.Disconnected;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.Now;

    /// <summary>
    /// 活跃任务数量
    /// </summary>
    public int ActiveTasks { get; set; }

    /// <summary>
    /// 错误计数
    /// </summary>
    public int ErrorCount { get; set; }

    /// <summary>
    /// 警告计数
    /// </summary>
    public int WarningCount { get; set; }
}
