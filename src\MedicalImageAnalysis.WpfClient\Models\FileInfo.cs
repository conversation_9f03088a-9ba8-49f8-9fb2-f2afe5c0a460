using System;
using System.IO;

namespace MedicalImageAnalysis.WpfClient.Models;

/// <summary>
/// 文件信息模型
/// </summary>
public class FileInfo
{
    /// <summary>
    /// 文件ID
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 文件名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 文件路径
    /// </summary>
    public string Path { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// 文件扩展名
    /// </summary>
    public string Extension { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime ModifiedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 文件类型
    /// </summary>
    public string FileType { get; set; } = string.Empty;

    /// <summary>
    /// 文件类型（别名，用于兼容）
    /// </summary>
    public string Type
    {
        get => FileType;
        set => FileType = value;
    }

    /// <summary>
    /// 文件名（别名，用于兼容）
    /// </summary>
    public string FileName => Name;

    /// <summary>
    /// 是否为DICOM文件
    /// </summary>
    public bool IsDicomFile => Extension.ToLower() is ".dcm" or ".dicom";

    /// <summary>
    /// 是否为图像文件
    /// </summary>
    public bool IsImageFile => Extension.ToLower() is ".jpg" or ".jpeg" or ".png" or ".bmp" or ".tiff" or ".tif";

    /// <summary>
    /// 格式化的文件大小
    /// </summary>
    public string FormattedSize
    {
        get
        {
            if (Size < 1024)
                return $"{Size} B";
            else if (Size < 1024 * 1024)
                return $"{Size / 1024.0:F1} KB";
            else if (Size < 1024 * 1024 * 1024)
                return $"{Size / (1024.0 * 1024.0):F1} MB";
            else
                return $"{Size / (1024.0 * 1024.0 * 1024.0):F1} GB";
        }
    }

    /// <summary>
    /// 从文件系统路径创建FileInfo
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>FileInfo实例</returns>
    public static FileInfo FromPath(string filePath)
    {
        var fileInfo = new System.IO.FileInfo(filePath);
        
        return new FileInfo
        {
            Name = fileInfo.Name,
            Path = filePath,
            Size = fileInfo.Length,
            Extension = fileInfo.Extension,
            CreatedTime = fileInfo.CreationTime,
            ModifiedTime = fileInfo.LastWriteTime,
            FileType = GetFileType(fileInfo.Extension)
        };
    }

    /// <summary>
    /// 根据扩展名获取文件类型
    /// </summary>
    /// <param name="extension">文件扩展名</param>
    /// <returns>文件类型描述</returns>
    private static string GetFileType(string extension)
    {
        return extension.ToLower() switch
        {
            ".dcm" or ".dicom" => "DICOM文件",
            ".jpg" or ".jpeg" => "JPEG图像",
            ".png" => "PNG图像",
            ".bmp" => "位图图像",
            ".tiff" or ".tif" => "TIFF图像",
            ".pdf" => "PDF文档",
            ".txt" => "文本文件",
            ".json" => "JSON文件",
            ".xml" => "XML文件",
            _ => "未知文件"
        };
    }
}
