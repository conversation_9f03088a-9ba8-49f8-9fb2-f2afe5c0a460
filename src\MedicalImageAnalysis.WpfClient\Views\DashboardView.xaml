<UserControl x:Class="MedicalImageAnalysis.WpfClient.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
        <Grid Margin="24">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,24">
                <TextBlock Text="医学影像解析系统" FontSize="24" FontWeight="Bold"
                         Foreground="{DynamicResource PrimaryHueMidBrush}" Margin="0,0,0,16"/>
                <TextBlock Text="欢迎使用医学影像解析系统，这是一个基于YOLOv11的智能医学影像分析平台。"
                         FontSize="14" TextWrapping="Wrap" LineHeight="20"
                         Foreground="{DynamicResource MaterialDesignBody}"/>
            </StackPanel>

            <!-- 统计卡片区域 -->
            <Grid Grid.Row="1" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- DICOM文件数量 -->
                <materialDesign:Card Grid.Column="0" Padding="16" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="DICOM文件" FontSize="12"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                <TextBlock Text="1,234" FontSize="28" FontWeight="Bold"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            </StackPanel>

                            <materialDesign:PackIcon Grid.Column="1" Kind="FileImage"
                                                   Width="32" Height="32"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Center"/>
                        </Grid>
                        <TextBlock Text="本月新增 +156" FontSize="12"
                                 Foreground="{DynamicResource SecondaryHueMidBrush}" Margin="0,8,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 训练模型数量 -->
                <materialDesign:Card Grid.Column="1" Padding="16" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="训练模型" FontSize="12"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                <TextBlock Text="42" FontSize="28" FontWeight="Bold"
                                         Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                            </StackPanel>

                            <materialDesign:PackIcon Grid.Column="1" Kind="Brain"
                                                   Width="32" Height="32"
                                                   Foreground="{DynamicResource SecondaryHueMidBrush}"
                                                   VerticalAlignment="Center"/>
                        </Grid>
                        <TextBlock Text="本月新增 +8" FontSize="12"
                                 Foreground="{DynamicResource SecondaryHueMidBrush}" Margin="0,8,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 标注数量 -->
                <materialDesign:Card Grid.Column="2" Padding="16" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="智能标注" FontSize="12"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                <TextBlock Text="5,678" FontSize="28" FontWeight="Bold"
                                         Foreground="Orange"/>
                            </StackPanel>

                            <materialDesign:PackIcon Grid.Column="1" Kind="Draw"
                                                   Width="32" Height="32"
                                                   Foreground="Orange"
                                                   VerticalAlignment="Center"/>
                        </Grid>
                        <TextBlock Text="本月新增 +892" FontSize="12"
                                 Foreground="Green" Margin="0,8,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 系统状态 -->
                <materialDesign:Card Grid.Column="3" Padding="16" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="系统状态" FontSize="12"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                <TextBlock Text="正常" FontSize="28" FontWeight="Bold"
                                         Foreground="Green"/>
                            </StackPanel>

                            <materialDesign:PackIcon Grid.Column="1" Kind="CheckCircle"
                                                   Width="32" Height="32"
                                                   Foreground="Green"
                                                   VerticalAlignment="Center"/>
                        </Grid>
                        <TextBlock Text="运行时间 24天" FontSize="12"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                 Margin="0,8,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- 功能区域 -->
            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧功能卡片 -->
                <StackPanel Grid.Column="0" Margin="0,0,12,0">
                    <TextBlock Text="主要功能" FontSize="18" FontWeight="Medium"
                             Foreground="{DynamicResource MaterialDesignBody}" Margin="0,0,0,8"/>

                    <UniformGrid Columns="2" Margin="0,16,0,0">
                        <!-- DICOM查看器 -->
                        <materialDesign:Card Padding="16" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2" Cursor="Hand">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileImage"
                                                       Width="48" Height="48"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"/>
                                <StackPanel Margin="16,0,0,0">
                                    <TextBlock Text="DICOM查看器" FontWeight="Medium" FontSize="16"/>
                                    <TextBlock Text="查看和分析DICOM医学影像文件"
                                             FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             TextWrapping="Wrap"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 模型训练 -->
                        <materialDesign:Card Padding="16" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2" Cursor="Hand">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="School"
                                                       Width="48" Height="48"
                                                       Foreground="{DynamicResource SecondaryHueMidBrush}"
                                                       VerticalAlignment="Center"/>
                                <StackPanel Margin="16,0,0,0">
                                    <TextBlock Text="模型训练" FontWeight="Medium" FontSize="16"/>
                                    <TextBlock Text="训练YOLOv11深度学习模型"
                                             FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             TextWrapping="Wrap"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 智能标注 -->
                        <materialDesign:Card Padding="16" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2" Cursor="Hand">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Draw"
                                                       Width="48" Height="48"
                                                       Foreground="Orange"
                                                       VerticalAlignment="Center"/>
                                <StackPanel Margin="16,0,0,0">
                                    <TextBlock Text="智能标注" FontWeight="Medium" FontSize="16"/>
                                    <TextBlock Text="自动生成和优化图像标注"
                                             FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             TextWrapping="Wrap"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 数据管理 -->
                        <materialDesign:Card Padding="16" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2" Cursor="Hand">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Database"
                                                       Width="48" Height="48"
                                                       Foreground="DarkOrange"
                                                       VerticalAlignment="Center"/>
                                <StackPanel Margin="16,0,0,0">
                                    <TextBlock Text="数据管理" FontWeight="Medium" FontSize="16"/>
                                    <TextBlock Text="管理医学影像数据和元数据"
                                             FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             TextWrapping="Wrap"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>
                    </UniformGrid>
                </StackPanel>

                <!-- 右侧系统信息 -->
                <StackPanel Grid.Column="1" Margin="12,0,0,0">
                    <TextBlock Text="系统信息" FontSize="18" FontWeight="Medium"
                             Foreground="{DynamicResource MaterialDesignBody}" Margin="0,0,0,8"/>

                    <!-- 系统监控 -->
                    <materialDesign:Card Padding="16" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2">
                        <StackPanel>
                            <TextBlock Text="系统监控" FontWeight="Medium" Margin="0,0,0,16"/>

                            <!-- CPU使用率 -->
                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="CPU" VerticalAlignment="Center"/>
                                <ProgressBar Grid.Column="1" Value="45" Maximum="100"
                                           Height="6" Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           Background="{DynamicResource MaterialDesignDivider}" Margin="8,0"/>
                                <TextBlock Grid.Column="2" Text="45%" VerticalAlignment="Center"/>
                            </Grid>

                            <!-- 内存使用率 -->
                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="内存" VerticalAlignment="Center"/>
                                <ProgressBar Grid.Column="1" Value="68" Maximum="100"
                                           Height="6" Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           Background="{DynamicResource MaterialDesignDivider}" Margin="8,0"/>
                                <TextBlock Grid.Column="2" Text="68%" VerticalAlignment="Center"/>
                            </Grid>

                            <!-- 磁盘使用率 -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="磁盘" VerticalAlignment="Center"/>
                                <ProgressBar Grid.Column="1" Value="32" Maximum="100"
                                           Height="6" Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           Background="{DynamicResource MaterialDesignDivider}" Margin="8,0"/>
                                <TextBlock Grid.Column="2" Text="32%" VerticalAlignment="Center"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 最近活动 -->
                    <materialDesign:Card Padding="16" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2">
                        <StackPanel>
                            <TextBlock Text="最近活动" FontWeight="Medium" Margin="0,0,0,16"/>

                            <StackPanel>
                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Ellipse Grid.Column="0" Width="12" Height="12" Margin="4"
                                           Fill="Green"/>
                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="模型训练完成" FontSize="12" FontWeight="Medium"/>
                                        <TextBlock Text="2分钟前" FontSize="12"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </StackPanel>
                                </Grid>

                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Ellipse Grid.Column="0" Width="12" Height="12" Margin="4"
                                           Fill="{DynamicResource PrimaryHueMidBrush}"/>
                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="DICOM文件上传" FontSize="12" FontWeight="Medium"/>
                                        <TextBlock Text="5分钟前" FontSize="12"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </StackPanel>
                                </Grid>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Ellipse Grid.Column="0" Width="12" Height="12" Margin="4"
                                           Fill="Orange"/>
                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="智能标注生成" FontSize="12" FontWeight="Medium"/>
                                        <TextBlock Text="10分钟前" FontSize="12"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
